'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Carousel, CarouselApi, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import { placeholders } from '@/lib/placeholders';
import { cn } from '@/lib/utils';
import { urlFor } from '@/sanity/lib/image';
import { internalGroqTypeReferenceTo, SanityImageCrop, SanityImageHotspot } from '@/sanity/types';
import Autoplay from 'embla-carousel-autoplay';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import * as React from 'react';
import { useState } from 'react';

export interface Slide {
  image?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  mobileImage?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  alt?: string;
  href?: string;
  title?: string;
  description?: string;
  hideTitle?: boolean;
  hideTitleMobile?: boolean;
  isRolexSlide?: boolean;
  decreasePadding?: boolean;
  _key: string;
}

interface HeroCarouselProps {
  slides: Slide[];
  isRolex?: boolean;
}

export default function HeroCarousel({ slides, isRolex = false }: HeroCarouselProps) {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  React.useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  return (
    <section className={cn('relative mb-[90px] overflow-hidden', !isRolex && 'px-0 sm:px-[7%]')}>
      <Carousel
        opts={{
          align: 'start',
          loop: true,
        }}
        plugins={[
          Autoplay({
            delay: 5000,
          }),
        ]}
        className={cn('w-full rounded-none! mx-auto')}
        setApi={setApi}
      >
        <CarouselContent>
          {slides.map(
            ({
              href,
              image,
              alt,
              mobileImage,
              title,
              description,
              hideTitle,
              hideTitleMobile,
              isRolexSlide,
              decreasePadding,
            }) => (
              <CarouselItem key={alt}>
                <Link href={href ?? '/'}>
                  <Card className={cn('cursor-pointer overflow-hidden rounded-none')}>
                    <CardContent
                      className={cn(
                        'relative aspect-[1.1] sm:aspect-[2.32/1] items-center justify-center p-0',
                        isRolex && 'aspect-none w-full h-[calc(100vw*1.375)] md:h-auto'
                      )}
                    >
                      <Image
                        src={urlFor(image || { _type: 'image' })?.url() ?? placeholders.imageCta.image}
                        alt={title ?? placeholders.imageCta.title}
                        className="hidden object-cover md:block"
                        fill
                        style={{ objectFit: 'cover' }}
                      />
                      <Image
                        src={urlFor(mobileImage || { _type: 'image' })?.url() ?? placeholders.imageCta.image}
                        alt={title ?? placeholders.imageCta.description}
                        className="object-cover md:hidden"
                        fill
                        style={{ objectFit: 'cover' }}
                      />
                      {title && (
                        <div
                          className={cn(
                            'absolute left-1/2 bottom-10 md:top-1/2 flex -translate-x-1/2 md:translate-x-[-30%] flex-col items-center text-center md:text-left md:items-start justify-center font-rolex text-rolex-brown md:-translate-y-1/2 md:left-[60%]',
                            (hideTitle || hideTitleMobile) && 'hidden',
                            hideTitleMobile && !hideTitle && 'md:flex',
                            decreasePadding && 'md:left-[70%] w-screen',
                            isRolexSlide && 'rolex bg-transparent'
                          )}
                        >
                          <p className="font-bold md:text-2xl">{title}</p>
                          <h2
                            className={cn(
                              'w-full md:text-5xl',
                              isRolexSlide && '!font-rolex !font-bold !text-rolex-brown'
                            )}
                          >
                            {description}
                          </h2>
                          <Button className="btn rolex-primary mt-2 rounded-full md:mt-6">Discover</Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </Link>
              </CarouselItem>
            )
          )}
        </CarouselContent>
        <div className="absolute left-4 top-1/2 hidden -translate-y-1/2 sm:block">
          <Button
            variant="outline"
            size="icon"
            className="size-8 rounded-full bg-white/70 p-0"
            onClick={() => api?.scrollPrev()}
          >
            <ChevronLeft className="size-4" />
            <span className="sr-only">Previous slide</span>
          </Button>
        </div>
        <div className="absolute right-4 top-1/2 hidden -translate-y-1/2 sm:block">
          <Button
            variant="outline"
            size="icon"
            className="size-8 rounded-full bg-white/70 p-0"
            onClick={() => api?.scrollNext()}
          >
            <ChevronRight className="size-4" />
            <span className="sr-only">Next slide</span>
          </Button>
        </div>
        <div className={cn('absolute bottom-4 left-1/2 flex -translate-x-1/2 items-center space-x-2')}>
          {slides.map((_, index) => (
            <div
              key={index}
              className={cn(
                'rounded-full transition-all duration-300 ease-in-out origin-center',
                index === current - 1 ? (isRolex ? 'bg-white w-10' : 'bg-accent w-10') : 'w-5 bg-white/50',
                isRolex && 'h-1.5 min-h-[4px] max-h-[4px] rounded-full transition-all duration-200 ease-in-out'
              )}
            />
          ))}
        </div>
        {!isRolex && (
          <div className="absolute bottom-4 right-4 rounded bg-black/50 px-2 py-1 text-sm text-white">
            {current} /{count}
          </div>
        )}
      </Carousel>
    </section>
  );
}
