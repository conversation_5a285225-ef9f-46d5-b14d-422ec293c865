import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface ImageCtaProps {
  imageSrc: string;
  imageAlt: string;
  title: string;
  description: string;
  linkHref: string;
  linkText: string;
}

export const CtaButton = ({
  href,
  text,
  isTransparent,
  className,
}: {
  href: string;
  text: string;
  isTransparent?: boolean;
  className?: string;
}) => (
  <Button
    asChild
    variant="outline"
    className={cn('w-fit text-foreground-500', isTransparent && 'bg-transparent text-background', className)}
  >
    <Link href={href} className="group flex items-center">
      {text}
      <ArrowRight
        className={cn(
          'ml-2 size-4 transition-transform duration-200 group-hover:translate-x-0.5',
          isTransparent && 'transition-colors'
        )}
      />
    </Link>
  </Button>
);

export function ImageCta({ imageSrc, imageAlt, title, description, linkHref, linkText }: ImageCtaProps) {
  return (
    <section className="mb-[90px] px-4 sm:px-[14%]">
      <Card className="mx-auto max-w-full overflow-hidden shadow-md">
        <CardContent className="flex flex-col p-0 md:h-[500px] md:flex-row lg:h-[600px]">
          <div className="relative h-64 w-full md:h-full md:w-[65%]">
            <Image src={imageSrc} alt={imageAlt} fill className="object-cover" />
          </div>
          <div className="mx-auto flex flex-col justify-center space-y-6 p-6 md:w-1/3">
            <div>
              <h3 className="mb-4 text-balance text-2xl font-medium md:text-4xl">{title}</h3>
              <p className="text-foreground-500/90 md:text-lg">{description}</p>
            </div>
            <CtaButton href={linkHref} text={linkText} />
          </div>
        </CardContent>
      </Card>
    </section>
  );
}
