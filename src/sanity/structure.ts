import type { StructureResolver } from 'sanity/structure';

export const structure: StructureResolver = (S) =>
  S.list()
    .title('Content')
    .items([
      // Site Settings
      S.listItem()
        .title('Site Settings')
        .child(S.document().title('Site Settings').schemaType('siteMeta').documentId('siteMeta')),
      S.divider(),

      // Pages parent item
      S.listItem()
        .title('Pages')
        .child(
          S.list()
            .title('Pages')
            .items([
              // Regular Pages
              S.listItem()
                .title('Regular Pages')
                .child(
                  S.documentTypeList('page')
                    .title('Regular Pages')
                    .filter('_type == "page" && !(slug.current match "/rolex/*")')
                ),
              // Rolex Specific Pages
              S.listItem()
                .title('Rolex Pages')
                .child(
                  S.documentTypeList('page')
                    .title('Rolex Pages')
                    .filter('_type == "page" && slug.current match "/rolex/*"')
                ),
            ])
        ),

      // CMS Collections
      S.listItem()
        .title('Collections')
        .child(
          S.list()
            .title('Collections')
            .items([
              // Rolex Watches
              S.listItem()
                .title('Rolex Watches')
                .child(S.documentTypeList('rolexWatch').title('Rolex Collections').filter('_type == "rolexWatch"')),
              // Rolex Posts
              S.listItem()
                .title('World of Rolex Posts')
                .child(S.documentTypeList('rolexPost').title('World of Rolex Posts').filter('_type == "rolexPost"')),
              // Watch product
              S.listItem()
                .title('Watch Product')
                .child(S.documentTypeList('rolexPost').title('Watch Products').filter('_type == "watchProduct"')),
              // Jewelry products
              S.listItem()
                .title('Jewelry Product')
                .child(S.documentTypeList('rolexPost').title('Jewelry Products').filter('_type == "jewelryProduct"')),
            ])
        ),

      // Hide Collection and Page Types from Content Root List
      ...S.documentTypeListItems().filter(
        (item) =>
          [
            'siteMeta',
            'imageContent',
            'rolexExploreCarousel',
            'rolexContactCard',
            'rolexWatch',
            'rolexPost',
            'page',
          ].indexOf(item.getId() as string) === -1
      ),
    ]);
