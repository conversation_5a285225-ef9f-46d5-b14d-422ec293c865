import { client } from '@/sanity/lib/client';
import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import AdmZip from 'adm-zip';

// Function to upload image to Sanity
async function uploadImageToSanity(imageBuffer: Buffer, fileName: string) {
  try {
    const asset = await client.assets.upload('image', imageBuffer, {
      filename: fileName,
    });
    return {
      _type: 'image',
      asset: {
        _ref: asset._id,
        _type: 'reference',
      },
    };
  } catch (error) {
    console.error(`Error uploading image ${fileName}:`, error);
    return null;
  }
}

export async function POST(request: Request) {
  // Optional: Add authentication for security
  const authHeader = request.headers.get('authorization');
  if (!authHeader || authHeader !== `Bearer ${process.env.SYNC_API_KEY}`) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  try {
    const [imageResponse, articleResponse] = await Promise.all([
      fetch('http://************:9600/CS_WebService/OsService.svc/articleimages'),
      fetch('http://************:9600/CS_WebService/OsService.svc/articles'),
    ]);

    const articleData = await articleResponse.json();
    const imageData = await imageResponse.json();

    if (!imageData.Success || !articleData.Success) {
      throw new Error('API response indicates failure');
    }

    // Process image data from zip
    const bytes = imageData.data.Checksum;
    const buffer = Buffer.from(bytes);

    // Create AdmZip instance to extract images from buffer
    const zip = new AdmZip(buffer);
    const zipEntries = zip.getEntries();

    // Create a map to store images by EAN (filename without extension)
    const imageMap = new Map<string, any>();

    // Process each image in the zip
    for (const entry of zipEntries) {
      if (!entry.isDirectory && entry.entryName.match(/\.(jpg|jpeg|png|webp)$/i)) {
        try {
          // Extract filename without extension to match with EAN
          const fileName = entry.entryName.split('/').pop() || '';
          const eanFromFileName = fileName.replace(/\.(jpg|jpeg|png|webp)$/i, '');

          // Get image buffer
          const imageBuffer = entry.getData();

          // Upload to Sanity
          const uploadedImage = await uploadImageToSanity(imageBuffer, fileName);

          if (uploadedImage) {
            imageMap.set(eanFromFileName, uploadedImage);
            console.log(`Successfully uploaded image for EAN: ${eanFromFileName}`);
          }
        } catch (error) {
          console.error(`Error processing image ${entry.entryName}:`, error);
        }
      }
    }

    const articles = articleData.data;

    // Process articles: Create or update Sanity documents
    const transaction = client.transaction();
    for (const article of articles) {
      const {
        Ean,
        Title,
        Description,
        Brand,
        Collection,
        Price,
        ArticleGroup,
        ArticleGroupId,
        ReferenceNo,
        Stock,
        ProductGroup,
        ProductLine,
        ArtikelSpecialData,
        Designations,
        specialData,
        specialAttributes,
        Guarantee,
      } = article;

      // Determine product type based on ArticleGroup
      let productType: string = '';
      let baseUrl: string = '';
      if (ArticleGroup === 'Watches' || ArticleGroup === 'Pre-Owned-Watches') {
        productType = 'watchProduct';
        baseUrl = 'watches';
      }
      if (ArticleGroup === 'Diamond-Jewelry' || ArticleGroup === '18ct-Jewelry') {
        productType = 'jewelryProduct';
        baseUrl = 'jewelry';
      }

      // Prepare product document
      const productId = `${productType}-${Ean}`;
      const product = {
        _id: productId,
        _type: productType,
        ean: Ean,
        title: Title,
        description: Designations.find((d: any) => d.LngIso === 'en')?.Beschreibung || Description,
        brand: Brand,
        collection: Collection,
        price: Price,
        articleGroup: {
          name: ArticleGroup,
          id: ArticleGroupId,
        },
        referenceNo: ReferenceNo,
        stock: Stock,
        productGroup: ProductGroup,
        productLine: ProductLine,
        specialData: specialData.map(({ AttributeName, AttributeValue }: any) => ({
          _key: uuidv4(),
          name: AttributeName,
          value: AttributeValue,
        })),
        specialAttributes: specialAttributes.map(({ AttributeName, AttributeValue }: any) => ({
          _key: uuidv4(),
          name: AttributeName,
          value: AttributeValue,
        })),
        slug: {
          _type: 'slug',
          current: `${baseUrl}/${Brand.toLowerCase()}/${Ean.toLowerCase()}`,
        },
        // Initialize optional properties as null
        watchDetails: null as {
          strapType?: string;
          strapColour?: string;
          caseWidth?: string;
          caseDepth?: string;
          caseShape?: string;
          caseMaterial?: string;
          glassType?: string;
          dial?: string;
          dialColor?: string;
          movement?: string;
          claspType?: string;
          waterproofRating?: number;
          warranty?: string;
          packaging?: string;
          sku?: string;
          IsDatum?: boolean;
          IsGangreserve?: boolean;
          IsMondphase?: boolean;
          IsSekunde?: boolean;
          IsTagDatum?: boolean;
          IsTaucher?: boolean;
          IsZweiteZeitZone?: boolean;
        } | null,
        jewelryDetails: null as {
          sizeType?: number;
          serviceInterval?: number;
          diamonds?: Array<{
            _key: string;
            carat: number;
            clarity: string;
            color: string;
            cut: string;
            cutQuality: string;
          }>;
        } | null,
        image: null as {
          _type: 'image';
          asset: {
            _ref: string;
            _type: 'reference';
          };
        } | null,
      };

      // Add watch-specific data if applicable
      if (ArticleGroup === 'Watches' || ArticleGroup === 'Pre-Owned-Watches') {
        const { ArtikelUhr } = ArtikelSpecialData;
        product.watchDetails = {
          strapType: ArtikelUhr.strapType,
          strapColour: ArtikelUhr.Armband,
          caseWidth: ArtikelUhr.Gehaeusebreite,
          caseShape: ArtikelUhr.Gehaeuseform,
          caseDepth: ArtikelUhr.Gehaeusehoehe,
          caseMaterial: ArtikelUhr.Gehaeusematerial,
          glassType: ArtikelUhr.Glastyp,
          dial: ArtikelUhr.Zifferblatt,
          dialColor: ArtikelUhr.ZifferblattFarbe,
          movement: ArtikelUhr.Uhrwerk,
          claspType: ArtikelUhr.Schliesse,
          waterproofRating: ArtikelUhr.Wasserdicht,
          warranty: Guarantee,
          packaging: '',
          sku: ArtikelUhr.LiefArtNr,
          IsDatum: ArtikelUhr.IsDatum, // 是否有日期显示
          IsGangreserve: ArtikelUhr.IsGangreserve, // 是否有动力储存显示
          IsMondphase: ArtikelUhr.IsMondphase, // 是否有月相功能
          IsSekunde: ArtikelUhr.IsSekunde, // 是否有秒针
          IsTagDatum: ArtikelUhr.IsTagDatum, // 是否有星期和日期显示
          IsTaucher: ArtikelUhr.IsTaucher, // 是否为潜水表
          IsZweiteZeitZone: ArtikelUhr.IsZweiteZeitZone, // 是否有第二时区功能
        };
      }

      // Add jewelry-specific data if applicable
      if (ArticleGroup === 'Diamond-Jewelry' || ArticleGroup === '18ct-Jewelry') {
        const { ArtikelSchmuck, DiamantenListe } = ArtikelSpecialData;
        product.jewelryDetails = {
          sizeType: ArtikelSchmuck.GroessenTyp,
          serviceInterval: ArtikelSchmuck.ServiceIntervall,
          diamonds: DiamantenListe.map((diamond: any) => ({
            _key: uuidv4(),
            carat: diamond.GesamtCarat,
            clarity: diamond.Clarity,
            color: diamond.Color,
            cut: diamond.Cut,
            cutQuality: diamond.CutQualitaet,
          })),
        };
      }

      // Attach main image if available
      const mainImage = imageMap.get(Ean);
      if (mainImage) {
        product.image = mainImage;
      }

      // Add to transaction (create or update)
      transaction.createIfNotExists(product).patch(productId, (p: any) => p.set(product));
    }

    // Commit transaction
    await transaction.commit();
    console.log('Successfully synced products to Sanity');

    return NextResponse.json(
      { message: 'Sync completed successfully' },
      {
        status: 200,
      }
    );
  } catch (error) {
    console.error('Error syncing products:', error);
    return NextResponse.json(
      {
        message: 'Sync failed',
        error: (error as Error).message,
      },
      { status: 500 }
    );
  }
}
