import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

// Load environment variables
try {
  dotenv.config({ path: '.env.local' });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
} catch (e) {
  // Continue with process.env
}

// 要测试的端口列表
const PORTS_TO_TEST = [25, 587, 465, 26, 940, 8001, 2525, 80];

// 邮件服务器配置
const EMAIL_HOST = 'relay.dnsexit.com';

// 测试邮件配置
const TEST_EMAIL_CONFIG = {
  title: 'Mr',
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>',
  phone: '1234567890',
  country: 'UK',
  city: 'London',
  storeAddress: 'Test Store Address',
  message: 'This is a test message for port connectivity testing.',
  acceptTerms: true,
};

/**
 * 测试指定端口的邮件发送功能
 * @param {number} port - 要测试的端口号
 * @returns {Promise<Object>} 测试结果
 */
async function testEmailPort(port) {
  console.log(`\n🔍 Testing port ${port}...`);

  try {
    // 创建不同端口配置的传输器
    const transporterConfig = {
      host: EMAIL_HOST,
      port: port,
      secure: port === 465, // 465端口通常使用SSL
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
      tls: {
        rejectUnauthorized: false,
      },
      // 设置连接超时
      connectionTimeout: 10000,
      greetingTimeout: 5000,
      socketTimeout: 10000,
    };

    const transporter = nodemailer.createTransport(transporterConfig);

    // 验证连接
    console.log(`   ⏳ Verifying connection for port ${port}...`);
    await transporter.verify();
    console.log(`   ✅ Port ${port} connection verified successfully`);

    // 准备测试邮件
    const mailOptions = {
      to: TEST_EMAIL_CONFIG.email,
      from: '<EMAIL>',
      subject: `[PORT TEST ${port}] Rolex Contact Form: Message from ${TEST_EMAIL_CONFIG.firstName} ${TEST_EMAIL_CONFIG.lastName}`,
      text: `
        [THIS IS A PORT CONNECTIVITY TEST - Port ${port}]
        
        Title: ${TEST_EMAIL_CONFIG.title}
        Name: ${TEST_EMAIL_CONFIG.firstName} ${TEST_EMAIL_CONFIG.lastName}
        Email: ${TEST_EMAIL_CONFIG.email}
        Phone: ${TEST_EMAIL_CONFIG.phone || 'Not provided'}
        Country: ${TEST_EMAIL_CONFIG.country}
        City: ${TEST_EMAIL_CONFIG.city}
        Store Address: ${TEST_EMAIL_CONFIG.storeAddress}

        Message:
        ${TEST_EMAIL_CONFIG.message}

        Terms Accepted: ${TEST_EMAIL_CONFIG.acceptTerms ? 'Yes' : 'No'}
        
        Test Port: ${port}
        Test Time: ${new Date().toISOString()}
      `,
    };

    // 发送测试邮件
    console.log(`   📧 Sending test email via port ${port}...`);
    const result = await transporter.sendMail(mailOptions);

    return {
      port,
      success: true,
      messageId: result.messageId,
      response: result.response,
      secure: transporterConfig.secure,
    };
  } catch (error) {
    return {
      port,
      success: false,
      error: error.message,
      code: error.code,
      secure: port === 465,
    };
  }
}

/**
 * 测试所有端口
 */
async function testAllPorts() {
  console.log('🚀 Starting email port connectivity test...');
  console.log(`📧 Email Host: ${EMAIL_HOST}`);
  console.log(`🔌 Testing ports: ${PORTS_TO_TEST.join(', ')}`);
  console.log(`👤 Email User: ${process.env.EMAIL_USER || 'NOT SET'}`);
  console.log(`🔑 Email Pass: ${process.env.EMAIL_PASS ? '***SET***' : 'NOT SET'}`);

  if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
    console.error('❌ EMAIL_USER or EMAIL_PASS environment variables are not set!');
    console.log('Please make sure your .env file contains:');
    console.log('EMAIL_USER=your_email_username');
    console.log('EMAIL_PASS=your_email_password');
    return;
  }

  const results = [];

  // 逐个测试每个端口
  for (const port of PORTS_TO_TEST) {
    const result = await testEmailPort(port);
    results.push(result);

    if (result.success) {
      console.log(`   ✅ Port ${port}: SUCCESS`);
    } else {
      console.log(`   ❌ Port ${port}: FAILED - ${result.error}`);
    }
  }

  // 输出总结报告
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));

  const successfulPorts = results.filter((r) => r.success);
  const failedPorts = results.filter((r) => !r.success);

  console.log(`✅ Successful ports (${successfulPorts.length}/${results.length}):`);
  successfulPorts.forEach((result) => {
    console.log(`   Port ${result.port}: ${result.secure ? 'SSL/TLS' : 'Plain'} - Message ID: ${result.messageId}`);
  });

  console.log(`\n❌ Failed ports (${failedPorts.length}/${results.length}):`);
  failedPorts.forEach((result) => {
    console.log(`   Port ${result.port}: ${result.error} ${result.code ? `(${result.code})` : ''}`);
  });

  // 推荐配置
  if (successfulPorts.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:');
    const recommendedPort =
      successfulPorts.find((r) => r.port === 587) || successfulPorts.find((r) => r.port === 465) || successfulPorts[0];

    console.log(`🎯 Recommended port: ${recommendedPort.port} (${recommendedPort.secure ? 'SSL/TLS' : 'Plain'})`);
    console.log('\n📝 Suggested configuration:');
    console.log(`{
  host: '${EMAIL_HOST}',
  port: ${recommendedPort.port},
  secure: ${recommendedPort.secure},
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
  tls: {
    rejectUnauthorized: false,
  }
}`);
  } else {
    console.log('\n❌ No working ports found. Please check:');
    console.log('   - Email credentials are correct');
    console.log('   - Network connectivity');
    console.log('   - Firewall settings');
    console.log('   - Email server configuration');
  }
}

// 运行测试
testAllPorts().catch(console.error);
