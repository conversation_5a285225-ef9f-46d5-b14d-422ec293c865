import { fetchAndExtractImages } from './fetch-and-extract-images.js';
import { ImageExtractor } from './fetch-and-extract-images-advanced.js';

/**
 * 示例1: 使用基础版本
 */
async function basicExample() {
  console.log('=== 基础版本示例 ===');

  const apiUrl = 'http://62.7.112.233:9600/CS_WebService/OsService.svc/articleimages';
  const outputDir = '../example-basic-output';

  const result = await fetchAndExtractImages(apiUrl, outputDir);

  if (result.success) {
    console.log(`✅ 成功解压 ${result.extractedFiles} 个文件`);
    console.log(`📁 输出目录: ${result.outputPath}`);
  } else {
    console.error(`❌ 解压失败: ${result.error}`);
  }
}

/**
 * 示例2: 使用高级版本 - 只提取JPG文件
 */
async function advancedExampleJpgOnly() {
  console.log('\n=== 高级版本示例 - 只提取JPG文件 ===');

  const config = {
    outputDir: '../example-jpg-only',
    overwrite: true,
    createTimestampFolder: false,
    logLevel: 'info',
    fileFilter: (filename) => filename.toLowerCase().endsWith('.jpg'),
  };

  const extractor = new ImageExtractor(config);
  const result = await extractor.fetchAndExtractImages();

  if (result.success) {
    console.log('✅ JPG文件提取完成');
    console.log(`📊 统计: ${JSON.stringify(result.stats, null, 2)}`);
  } else {
    console.error(`❌ 提取失败: ${result.error}`);
  }
}

/**
 * 示例3: 使用高级版本 - 限制文件大小
 */
async function advancedExampleSizeLimit() {
  console.log('\n=== 高级版本示例 - 限制文件大小 ===');

  const config = {
    outputDir: '../example-size-limit',
    overwrite: true,
    createTimestampFolder: true,
    logLevel: 'debug',
    maxFileSize: 50 * 1024, // 50KB限制
  };

  const extractor = new ImageExtractor(config);
  const result = await extractor.fetchAndExtractImages();

  if (result.success) {
    console.log('✅ 大小限制提取完成');
    console.log(`📊 统计: ${JSON.stringify(result.stats, null, 2)}`);
  } else {
    console.error(`❌ 提取失败: ${result.error}`);
  }
}

/**
 * 示例4: 使用高级版本 - 自定义文件过滤器
 */
async function advancedExampleCustomFilter() {
  console.log('\n=== 高级版本示例 - 自定义文件过滤器 ===');

  const config = {
    outputDir: '../example-custom-filter',
    overwrite: true,
    createTimestampFolder: false,
    logLevel: 'info',
    // 只提取文件名包含特定数字模式的文件
    fileFilter: (filename) => {
      // 提取文件名中包含 "930101" 的文件
      return filename.includes('930101');
    },
  };

  const extractor = new ImageExtractor(config);
  const result = await extractor.fetchAndExtractImages();

  if (result.success) {
    console.log('✅ 自定义过滤提取完成');
    console.log(`📊 统计: ${JSON.stringify(result.stats, null, 2)}`);
  } else {
    console.error(`❌ 提取失败: ${result.error}`);
  }
}

/**
 * 示例5: 批量处理多个API端点
 */
async function batchProcessingExample() {
  console.log('\n=== 批量处理示例 ===');

  const apiEndpoints = [
    'http://62.7.112.233:9600/CS_WebService/OsService.svc/articleimages',
    // 可以添加更多API端点
  ];

  for (let i = 0; i < apiEndpoints.length; i++) {
    const apiUrl = apiEndpoints[i];
    console.log(`\n处理API端点 ${i + 1}/${apiEndpoints.length}: ${apiUrl}`);

    const config = {
      apiUrl: apiUrl,
      outputDir: `../batch-output/api-${i + 1}`,
      overwrite: true,
      createTimestampFolder: true,
      logLevel: 'info',
    };

    const extractor = new ImageExtractor(config);
    const result = await extractor.fetchAndExtractImages();

    if (result.success) {
      console.log(`✅ API端点 ${i + 1} 处理完成`);
    } else {
      console.error(`❌ API端点 ${i + 1} 处理失败: ${result.error}`);
    }
  }
}

/**
 * 主函数 - 运行所有示例
 */
async function main() {
  console.log('🚀 开始运行图片提取示例...\n');

  try {
    // 运行基础示例
    await basicExample();

    // 运行高级示例
    await advancedExampleJpgOnly();
    await advancedExampleSizeLimit();
    await advancedExampleCustomFilter();

    // 运行批量处理示例
    await batchProcessingExample();

    console.log('\n🎉 所有示例运行完成！');
  } catch (error) {
    console.error('❌ 示例运行过程中出错:', error);
  }
}

// 如果直接运行此脚本
const isMainModule = process.argv[1] && import.meta.url.includes(process.argv[1].split('/').pop());
if (isMainModule) {
  main().catch(console.error);
}

export {
  basicExample,
  advancedExampleJpgOnly,
  advancedExampleSizeLimit,
  advancedExampleCustomFilter,
  batchProcessingExample,
};
