#!/usr/bin/env node

/**
 * <PERSON>ript to update Rolex watch ranking field from Excel data
 *
 * Usage: node scripts/update-rolex-ranking.js [--rmc=RMC1,RMC2,...] [--execute]
 *
 * This script:
 * 1. Reads technical specifications Excel file
 * 2. Finds watches in Sanity by RMC
 * 3. Updates only the ranking field
 */

import XLSX from 'xlsx';
import { createClient } from '@sanity/client';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
try {
  dotenv.config({ path: '.env.local' });
  console.log('✅ Environment variables loaded');
} catch (e) {
  console.log('⚠️  Error loading .env.local:', e.message);
}

// Validate environment variables
const requiredEnvVars = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN,
};

function validateEnvironment() {
  console.log('🔍 Checking environment variables...');
  console.log('Project ID:', requiredEnvVars.projectId ? '✅ Set' : '❌ Missing');
  console.log('Dataset:', requiredEnvVars.dataset ? '✅ Set' : '❌ Missing');
  console.log('Token:', requiredEnvVars.token ? '✅ Set' : '❌ Missing');

  const missing = Object.entries(requiredEnvVars)
    .filter(([, value]) => !value)
    .map(([key]) => key);

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach((key) => {
      const envVar =
        key === 'projectId'
          ? 'NEXT_PUBLIC_SANITY_PROJECT_ID'
          : key === 'dataset'
            ? 'NEXT_PUBLIC_SANITY_DATASET'
            : 'SANITY_API_TOKEN';
      console.error(`  - ${envVar}`);
    });
    console.error('\nPlease set these in your .env.local file or environment.');
    process.exit(1);
  }
}

// Sanity client configuration
let client;
try {
  validateEnvironment();
  console.log('🔧 Initializing Sanity client...');
  client = createClient({
    projectId: requiredEnvVars.projectId,
    dataset: requiredEnvVars.dataset,
    useCdn: false,
    token: requiredEnvVars.token,
    apiVersion: '2024-01-01',
  });
  console.log('✅ Sanity client initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Sanity client:', error.message);
  process.exit(1);
}

// File paths
const DATA_DIR = './scripts/Official Rolex Selection';
const SPECS_FILE = path.join(DATA_DIR, 'technical_specifications_rolex_selection_en.xlsx');

/**
 * Read and parse Excel file
 */
function readExcelFile(filePath, sheetName = null) {
  try {
    const workbook = XLSX.readFile(filePath);
    const sheet = sheetName || workbook.SheetNames[0];
    return XLSX.utils.sheet_to_json(workbook.Sheets[sheet]);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return [];
  }
}

/**
 * Convert ranking to number
 */
function convertToNumber(value) {
  if (value === null || value === undefined || value === '') return null;
  const num = Number(value);
  return isNaN(num) ? null : num;
}

/**
 * Find existing watch in Sanity by RMC
 */
async function findExistingWatch(rmc) {
  if (!rmc) return null;

  try {
    const query = `*[_type == "rolexWatch" && rmc == $rmc][0] {
      _id,
      title,
      rmc,
      ranking
    }`;

    const result = await client.fetch(query, { rmc });
    return result;
  } catch (error) {
    console.error(`   ❌ Error finding existing watch: ${error.message}`);
    return null;
  }
}

/**
 * Update watch ranking in Sanity
 */
async function updateWatchRanking(existingWatch, newRanking, dryRun = false) {
  try {
    const currentRanking = existingWatch.ranking || 'Not set';

    console.log(`   📊 Current ranking: ${currentRanking}`);
    console.log(`   📊 New ranking: ${newRanking || 'Not provided'}`);

    // Check if ranking needs to be updated
    if (existingWatch.ranking === newRanking) {
      console.log(`   ✅ Ranking already up to date`);
      return true;
    }

    if (dryRun) {
      console.log(`   🧪 DRY RUN - Would update ranking from "${currentRanking}" to "${newRanking}"`);
      return true;
    }

    // Perform update - convert to number
    const updateData = { ranking: convertToNumber(newRanking) };
    const result = await client.patch(existingWatch._id).set(updateData).commit();
    console.log(`   ✅ Successfully updated ranking: ${result._id}`);

    return true;
  } catch (error) {
    console.error(`   ❌ Error updating watch ranking: ${error.message}`);
    return false;
  }
}

/**
 * Main execution function
 */
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  const rmcFilter = args.find((arg) => arg.startsWith('--rmc='))?.split('=')[1];

  console.log('🏗️  Rolex Watch Ranking Update Script');
  console.log('=====================================');

  // Validate files exist
  if (!fs.existsSync(SPECS_FILE)) {
    console.error(`❌ Specifications file not found: ${SPECS_FILE}`);
    process.exit(1);
  }

  // Read Excel data
  console.log('📊 Reading Excel file...');
  const excelData = readExcelFile(SPECS_FILE, 'Watches List');

  if (excelData.length === 0) {
    console.error('❌ No data found in specifications Excel file');
    process.exit(1);
  }

  console.log(`Found ${excelData.length} watch specifications`);

  // Filter by RMC if specified
  let filteredData = excelData;
  if (rmcFilter) {
    const rmcList = rmcFilter.split(',').map((rmc) => rmc.trim());
    filteredData = excelData.filter((row) => rmcList.includes(row.RMC));
    console.log(`Filtered to ${filteredData.length} watches for RMC codes: ${rmcList.join(', ')}`);
  }

  // Filter only rows with RMC data
  const validRows = filteredData.filter((row) => row.RMC);
  console.log(`Processing ${validRows.length} watches with valid RMC codes`);

  if (dryRun) {
    console.log('\n🧪 DRY RUN MODE - No actual changes will be made');
  }

  // Process each watch
  let successCount = 0;
  let failureCount = 0;
  let notFoundCount = 0;

  for (let i = 0; i < validRows.length; i++) {
    const excelRow = validRows[i];
    const rmc = excelRow.RMC;
    const ranking = convertToNumber(excelRow.Ranking);

    console.log(`\n[${i + 1}/${validRows.length}] Processing: ${excelRow.ModelName || 'Unknown'} (${rmc})`);

    try {
      // Find existing watch in Sanity
      const existingWatch = await findExistingWatch(rmc);

      if (existingWatch) {
        console.log(`   ✅ Found existing watch in Sanity: ${existingWatch.title}`);
        console.log(`   ID: ${existingWatch._id}`);

        // Update ranking
        const success = await updateWatchRanking(existingWatch, ranking, dryRun);
        if (success) {
          successCount++;
        } else {
          failureCount++;
        }
      } else {
        console.log(`   ⚠️  Watch not found in Sanity`);
        notFoundCount++;
      }
    } catch (error) {
      console.error(`   ❌ Error processing Excel row: ${error.message}`);
      failureCount++;
    }
  }

  // Summary
  console.log('\n📈 Summary:');
  console.log(`  ✅ Successfully processed: ${successCount}`);
  console.log(`  ❌ Failed: ${failureCount}`);
  console.log(`  ⚠️  Not found in Sanity: ${notFoundCount}`);
  console.log(`  📊 Total processed: ${validRows.length}`);

  if (dryRun) {
    console.log('\n💡 Usage options:');
    console.log('  --execute              Actually update rankings in Sanity');
    console.log('  --rmc=M123456,M789012  Update only specific RMC codes (comma-separated)');
    console.log('\nExample: node scripts/update-rolex-ranking.js --rmc=M126610LN-0001 --execute');
  }
}

// Run the script
console.log('🚀 Starting script execution...');
main().catch(console.error);
