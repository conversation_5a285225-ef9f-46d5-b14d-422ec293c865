# Rolex Watch Batch Update Script 使用说明

## 📋 功能概述

`populate-rolex-watches-new.js` 脚本实现以下功能：

1. **批量查询手表** - 在 Sanity 数据库中查找所有包含 parentWatch 数据的手表
2. **Excel 数据匹配** - 在 Excel 文件中查找对应的手表数据和价格数据
3. **批量数据更新** - 将 Excel 中的类型、材质、尺寸、价格等数据更新到 Sanity

## 🚀 使用方法

### 基本语法

```bash
node scripts/populate-rolex-watches-new.js [--rmc=RMC1,RMC2,...] [--execute]
```

### 参数说明

- `--rmc=RMC1,RMC2,...` - **可选**，指定要更新的RMC代码列表（逗号分隔）
- `--execute` - **可选**，实际执行更新（不加此参数为预览模式）

### 使用示例

#### 1. 预览模式（推荐先使用）

```bash
# 查看会更新什么数据，但不实际执行（所有手表）
node scripts/populate-rolex-watches-new.js

# 查看特定RMC的更新数据
node scripts/populate-rolex-watches-new.js --rmc=M126610LN-0001,M126505-0001
```

#### 2. 执行批量更新

```bash
# 实际执行所有手表的数据更新
node scripts/populate-rolex-watches-new.js --execute

# 只更新特定RMC代码的手表
node scripts/populate-rolex-watches-new.js --rmc=M126610LN-0001,M126505-0001 --execute
```

#### 3. 单个手表更新

```bash
# 只更新一个特定的手表
node scripts/populate-rolex-watches-new.js --rmc=M126610LN-0001 --execute
```

## 📊 更新的数据字段

脚本会从 Excel 中提取并更新以下字段：

### 主要字段

- **type** - 手表类型（来自 `FamilyName`）
- **material** - 材质（来自 `Spec_Material`）
- **size** - 尺寸（来自 `Spec_Diameter`，提取数字）
- **modelCase** - 表壳型号（来自 `Spec_ModelCase`）
- **rmc** - RMC代码（来自 `RMC`）
- **retailPrices** - 零售价格（来自价格Excel文件）
- **image** - 主图片（从本地图片目录上传到Sanity）⭐ **新增**
- **content[].image** - 内容中的rolexModelHero图片⭐ **新增**

## 🔍 数据匹配策略

脚本使用多种策略来匹配 Sanity 中的手表和 Excel 中的数据：

1. **RMC 匹配** - 通过 RMC 代码精确匹配
2. **标题完全匹配** - 手表标题与 Excel ModelName 完全匹配
3. **标题部分匹配** - 标题包含关系匹配
4. **H1 字段匹配** - 与 Excel H1 字段匹配

## 📝 输出示例

### 预览模式输出

```
🔧 Rolex Watch Update Script
============================
📋 Processing all watches with parentWatch data

📊 Reading Excel files...
✅ Found 150 records in specifications Excel
✅ Found 120 records in pricing Excel

🔍 Searching for all watches with parentWatch data...
✅ Found 25 watches with parentWatch data

[1/25] Processing: Rolex Submariner Date
   ID: abc123
   Current RMC: m126610ln-0001
   Current type: Not set
   Current material: Not set
   Current size: Not set
   Current retailPrices: Not set

🔍 Searching Excel data for watch: Rolex Submariner Date
✅ Found match using strategy 1
   Excel ModelName: Rolex Submariner Date
   Excel RMC: m126610ln-0001
   Excel FamilyName: Submariner

   💰 Found price data for RMC: m126610ln-0001
   📝 Extracting update data from Excel:
   📝 Type: Submariner
   📝 ModelCase: Oyster, 41 mm, Oystersteel
   📝 Material: Oystersteel
   📝 Size: 41mm
   📝 RMC: m126610ln-0001
   💰 Retail Prices: £7,350

🧪 DRY RUN - Updating watch in Sanity...
📋 Changes to be made:
   type: "Not set" → "Submariner"
   modelCase: "Not set" → "Oyster, 41 mm, Oystersteel"
   material: "Not set" → "Oystersteel"
   size: "Not set" → "41"
   retailPrices: "Not set" → "£7,350"
🧪 DRY RUN - No actual changes made

📊 Processing Summary:
   ✅ Successfully processed: 20
   ❌ Failed: 3
   ℹ️  No changes needed: 2
   📋 Total watches: 25

💡 To actually perform the updates, add --execute flag:
  node scripts/populate-rolex-watches-new.js --execute
```

### 执行模式输出

```
💾 Updating watch in Sanity...
📋 Changes to be made:
   type: "Not set" → "Submariner"
   modelCase: "Not set" → "Oyster, 41 mm, Oystersteel"
   material: "Not set" → "Oystersteel"
   size: "Not set" → "41"
   retailPrices: "Not set" → "£7,350"
✅ Successfully updated watch in Sanity
   Document ID: abc123
   Updated fields: type, modelCase, material, size, retailPrices

📊 Processing Summary:
   ✅ Successfully processed: 20
   ❌ Failed: 3
   ℹ️  No changes needed: 2
   📋 Total watches: 25

🎉 Batch update completed!
```

## ⚠️ 注意事项

1. **环境变量** - 确保设置了必要的 Sanity 环境变量：

   - `NEXT_PUBLIC_SANITY_PROJECT_ID`
   - `NEXT_PUBLIC_SANITY_DATASET`
   - `SANITY_API_TOKEN`

2. **Excel 文件** - 确保 Excel 文件存在：

   - `scripts/Official Rolex Selection/technical_specifications_rolex_selection_en.xlsx`
   - `scripts/Official Rolex Selection/RolexMaximumSuggestedRetailPrices_GB.xlsx`

3. **权限** - 确保 Sanity API Token 有写入权限

4. **备份** - 建议在执行前备份 Sanity 数据

5. **Parent Watch 条件** - 只处理包含 parentWatch 数据的手表

## 🛠️ 故障排除

### 找不到手表

```
ℹ️  No watches with parentWatch data found
```

**解决方案：**

- 检查 Sanity 中是否有手表设置了 parentWatch 字段
- 确认查询权限正确

### 找不到 Excel 匹配

```
⚠️  No matching Excel data found, skipping...
```

**解决方案：**

- 检查手表标题是否与 Excel 中的 ModelName 匹配
- 确认 RMC 代码是否正确
- 检查 Excel 文件是否包含该手表数据

### 权限错误

```
❌ Error updating watch in Sanity: Insufficient permissions
```

**解决方案：**

- 检查 SANITY_API_TOKEN 是否有效
- 确认 Token 有写入权限
- 检查环境变量设置

## 📚 相关文件

- **参考脚本**: `scripts/populate-rolex-watches.js`
- **规格数据**: `scripts/Official Rolex Selection/technical_specifications_rolex_selection_en.xlsx`
- **价格数据**: `scripts/Official Rolex Selection/RolexMaximumSuggestedRetailPrices_GB.xlsx`
- **环境配置**: `.env.local`

## 🔄 与原脚本的区别

| 功能     | 原脚本               | 新脚本               |
| -------- | -------------------- | -------------------- |
| 处理方式 | 单个手表（通过slug） | 批量处理所有手表     |
| 筛选条件 | 指定slug             | 包含parentWatch数据  |
| 价格数据 | 不处理               | 处理retailPrices字段 |
| 使用场景 | 单个手表更新         | 批量数据同步         |
