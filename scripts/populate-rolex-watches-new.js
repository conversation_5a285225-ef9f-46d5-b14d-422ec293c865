#!/usr/bin/env node

/**
 * <PERSON>ript to update Rolex watch data in Sanity from Excel
 *
 * Usage: node scripts/populate-rolex-watches-new.js [--execute]
 *
 * This script:
 * 1. Queries Sanity for all watches that have parentWatch data
 * 2. Finds matching data in Excel files (specs and prices)
 * 3. Updates the watch's type, material, size, and retailPrices in Sanity
 */

import XLSX from 'xlsx';
import { createClient } from '@sanity/client';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables from .env.local if available
try {
  dotenv.config({ path: '.env.local' });
} catch (e) {
  console.log('error', e);
  // dotenv not available, continue with process.env
}

// Validate required environment variables
const requiredEnvVars = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN,
};

function validateEnvironment() {
  const missing = Object.entries(requiredEnvVars)
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    .filter(([key, value]) => !value)
    .map(([key]) => key);

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach((key) => {
      const envVar =
        key === 'projectId'
          ? 'NEXT_PUBLIC_SANITY_PROJECT_ID'
          : key === 'dataset'
            ? 'NEXT_PUBLIC_SANITY_DATASET'
            : 'SANITY_API_TOKEN';
      console.error(`  - ${envVar}`);
    });
    console.error('\nPlease set these in your .env.local file or environment.');
    process.exit(1);
  }
}

// Sanity client configuration
let client;
try {
  validateEnvironment();
  client = createClient({
    projectId: requiredEnvVars.projectId,
    dataset: requiredEnvVars.dataset,
    useCdn: false,
    token: requiredEnvVars.token,
    apiVersion: '2024-01-01',
  });
} catch (error) {
  console.error('❌ Failed to initialize Sanity client:', error.message);
  process.exit(1);
}

// File paths
const DATA_DIR = './scripts/Official\ Rolex\ Selection';
const SPECS_FILE = path.join(DATA_DIR, 'technical_specifications_rolex_selection_en.xlsx');
const PRICES_FILE = path.join(DATA_DIR, 'RolexMaximumSuggestedRetailPrices_GB.xlsx');
const HERO_IMAGES_DIR = path.join(DATA_DIR, 'retailer-selection_webp/upright_watch_assets_landscape');
const GALLERY_IMAGES_DIR = path.join(DATA_DIR, '/retailer-selection_webp/model_gallery_assets_portrait');

/**
 * Read and parse Excel file
 */
function readExcelFile(filePath, sheetName = null) {
  try {
    const workbook = XLSX.readFile(filePath);
    const sheet = sheetName || workbook.SheetNames[0];
    return XLSX.utils.sheet_to_json(workbook.Sheets[sheet]);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return [];
  }
}

/**
 * Clean and format text
 */
function cleanText(text) {
  if (!text) return '';
  return text.toString().trim().replace(/\s+/g, ' ');
}

/**
 * Extract size from diameter specification
 */
function extractSize(diameter) {
  if (!diameter) return null;
  const match = diameter.toString().match(/(\d+(?:\.\d+)?)/);
  return match ? parseFloat(match[1]) : null;
}

/**
 * Find hero image file for a watch by RMC
 */
function findHeroImageForWatch(rmc) {
  if (!rmc) return null;

  try {
    // Check if images directory exists
    if (!fs.existsSync(HERO_IMAGES_DIR)) {
      console.log(`   ⚠️  Hero images directory not found: ${HERO_IMAGES_DIR}`);
      return null;
    }

    // List all files in the images directory
    const files = fs.readdirSync(HERO_IMAGES_DIR);

    // Look for image file matching the RMC
    const possibleNames = [
      `${rmc}.webp`,
      `${rmc.toLowerCase()}.webp`,
      `${rmc.toUpperCase()}.webp`,
      `${rmc}.jpg`,
      `${rmc.toLowerCase()}.jpg`,
      `${rmc.toUpperCase()}.jpg`,
      `${rmc}.png`,
      `${rmc.toLowerCase()}.png`,
      `${rmc.toUpperCase()}.png`,
    ];

    for (const fileName of possibleNames) {
      if (files.includes(fileName)) {
        const imagePath = path.join(HERO_IMAGES_DIR, fileName);
        console.log(`   🖼️  Found hero image: ${fileName}`);
        return imagePath;
      }
    }

    console.log(`   ⚠️  No hero image found for RMC: ${rmc}`);
    return null;
  } catch (error) {
    console.error(`   ❌ Error searching for hero image: ${error.message}`);
    return null;
  }
}

/**
 * Find gallery images for a watch by RMC
 */
function findGalleryImagesForWatch(rmc) {
  if (!rmc) return [];

  const galleryImages = [];
  const slideNames = ['slide1_portrait', 'slide2_portrait', 'slide3_portrait', 'slide4_portrait'];

  try {
    for (const slideName of slideNames) {
      const slideDir = path.join(GALLERY_IMAGES_DIR, slideName);

      if (!fs.existsSync(slideDir)) {
        console.log(`   ⚠️  Gallery directory not found: ${slideDir}`);
        continue;
      }

      const files = fs.readdirSync(slideDir);
      const possibleNames = [
        `${rmc}.webp`,
        `${rmc.toLowerCase()}.webp`,
        `${rmc.toUpperCase()}.webp`,
        `${rmc}.jpg`,
        `${rmc.toLowerCase()}.jpg`,
        `${rmc.toUpperCase()}.jpg`,
        `${rmc}.png`,
        `${rmc.toLowerCase()}.png`,
        `${rmc.toUpperCase()}.png`,
      ];

      let found = false;
      for (const fileName of possibleNames) {
        if (files.includes(fileName)) {
          const imagePath = path.join(slideDir, fileName);
          galleryImages.push(imagePath);
          console.log(`   🖼️  Found gallery image: ${slideName}/${fileName}`);
          found = true;
          break;
        }
      }

      if (!found) {
        console.log(`   ⚠️  No gallery image found for ${slideName}/${rmc}`);
      }
    }

    return galleryImages;
  } catch (error) {
    console.error(`   ❌ Error searching for gallery images: ${error.message}`);
    return [];
  }
}

/**
 * Upload image to Sanity and return asset reference
 */
async function uploadImageToSanity(imagePath, rmc, description = '') {
  try {
    console.log(`   📤 Uploading image to Sanity: ${path.basename(imagePath)}`);

    // Read the image file
    const imageBuffer = fs.readFileSync(imagePath);

    // Upload to Sanity
    const asset = await client.assets.upload('image', imageBuffer, {
      filename: path.basename(imagePath),
      title: `Rolex ${rmc} ${description}`,
      description: `Watch image for ${rmc} ${description}`,
    });

    console.log(`   ✅ Image uploaded successfully: ${asset._id}`);

    // Return the asset reference in the format Sanity expects
    return {
      _type: 'image',
      asset: {
        _type: 'reference',
        _ref: asset._id,
      },
    };
  } catch (error) {
    console.error(`   ❌ Error uploading image: ${error.message}`);
    return null;
  }
}

/**
 * Generate slug from family name and RMC
 */
function generateSlug(familyName, rmc) {
  if (!familyName || !rmc) return null;

  // Convert family name to slug format: lowercase, spaces to hyphens
  const categorySlug = familyName.toLowerCase().replace(/\s+/g, '-');

  // RMC should be uppercase
  const rmcUpper = rmc.toUpperCase();

  return `/rolex/watches/${categorySlug}/${rmcUpper}`;
}

/**
 * Find parent watch by family name
 */
async function findParentWatch(familyName) {
  if (!familyName) return null;

  try {
    // Try exact match first
    let query = `*[_type == "rolexWatch" && !defined(parentWatch) && title == $familyName][0]`;
    let result = await client.fetch(query, { familyName });

    if (result) {
      console.log(`   ✅ Found parent watch (exact): ${result.title}`);
      return {
        _type: 'reference',
        _ref: result._id,
      };
    }

    // Try fuzzy match (handle hyphens and spaces)
    const normalizedFamilyName = familyName.replace(/[-\s]/g, '');
    query = `*[_type == "rolexWatch" && !defined(parentWatch)] {
      _id,
      title,
      "normalizedTitle": title
    }`;

    const allParents = await client.fetch(query);

    for (const parent of allParents) {
      const normalizedTitle = parent.title.replace(/[-\s]/g, '');
      if (normalizedTitle.toLowerCase() === normalizedFamilyName.toLowerCase()) {
        console.log(`   ✅ Found parent watch (fuzzy): ${parent.title}`);
        return {
          _type: 'reference',
          _ref: parent._id,
        };
      }
    }

    console.log(`   ⚠️  No parent watch found for: ${familyName}`);
    return null;
  } catch (error) {
    console.error(`   ❌ Error finding parent watch: ${error.message}`);
    return null;
  }
}

/**
 * Generate content array for a watch
 */
async function generateWatchContent(excelRow, heroImageAsset, galleryImageAssets) {
  const content = [];

  // 1. Rolex Model Hero component
  const heroComponent = {
    _key: generateKey(),
    _type: 'rolexModelHero',
    heading: 'Rolex',
    subheading: excelRow.ModelName || '',
    body: [
      {
        _key: generateKey(),
        _type: 'block',
        children: [
          {
            _key: generateKey(),
            _type: 'span',
            marks: [],
            text: excelRow.Spec_ModelCase || '',
          },
        ],
        markDefs: [],
        style: 'normal',
      },
      {
        _key: generateKey(),
        _type: 'block',
        children: [
          {
            _key: generateKey(),
            _type: 'span',
            marks: [],
            text: excelRow.RMC || '',
          },
        ],
        markDefs: [],
        style: 'normal',
      },
    ],
    image: heroImageAsset,
  };
  content.push(heroComponent);

  // 2. Image Grid Cols component
  if (galleryImageAssets && galleryImageAssets.length > 0) {
    const imageGridComponent = {
      _key: generateKey(),
      _type: 'imageGridCols',
      columns: 4,
      images: galleryImageAssets.map((asset) => ({
        _key: generateKey(),
        alt: ' ',
        image: asset,
      })),

      spacing: 4, // Medium spacing
    };
    content.push(imageGridComponent);
  }

  // 3. Rolex Accordion component
  const accordionComponent = {
    _key: generateKey(),
    _type: 'rolexAccordionType',

    visible: true,
  };
  content.push(accordionComponent);

  // 4. Rolex Feature List component
  const features = [];

  // Add features based on Excel data
  if (excelRow.Reference) {
    features.push({
      _key: generateKey(),
      description: excelRow.Reference,
      title: 'Reference',
    });
  }

  if (excelRow.Spec_Movement) {
    features.push({
      _key: generateKey(),
      description: excelRow.Spec_Movement,
      title: 'Movement',
    });
  }

  if (excelRow.Spec_Dial) {
    features.push({
      _key: generateKey(),
      description: excelRow.Spec_Dial,
      title: 'Dial',
    });
  }

  if (excelRow.Spec_ModelCase) {
    features.push({
      _key: generateKey(),
      description: excelRow.Spec_ModelCase,
      title: 'Model Case',
    });
  }

  if (excelRow.Spec_Calibre) {
    features.push({
      _key: generateKey(),
      description: excelRow.Spec_Calibre,
      title: 'Calibre',
    });
  }

  if (excelRow.Spec_Certification) {
    features.push({
      _key: generateKey(),
      description: excelRow.Spec_Certification,
      title: 'Certification',
    });
  }

  if (excelRow.Spec_Bezel) {
    features.push({
      _key: generateKey(),
      description: excelRow.Spec_Bezel,
      title: 'Bezel',
    });
  }

  if (excelRow.Spec_PowerReserve) {
    features.push({
      _key: generateKey(),
      description: excelRow.Spec_PowerReserve,
      title: 'Power Reserve',
    });
  }

  if (excelRow.Spec_WaterResistance) {
    features.push({
      _key: generateKey(),
      description: excelRow.Spec_WaterResistance,
      title: 'Water-Resistance',
    });
  }

  if (excelRow.Spec_Bracelet) {
    features.push({
      _key: generateKey(),
      description: excelRow.Spec_Bracelet,
      title: 'Bracelet',
    });
  }

  // Generate brochure link
  const familyName = excelRow.FamilyName ? excelRow.FamilyName.toLowerCase().replace(/\s+/g, '-') : '';
  const rmc = excelRow.RMC ? excelRow.RMC.toLowerCase() : '';
  const brochureLink = `https://assets.rolex.com/api/brochure/en/${familyName}/${rmc}.pdf`;

  const featureListComponent = {
    _key: generateKey(),
    _type: 'rolexFeatureList',
    cta: {
      link: brochureLink,
      text: 'Download Brochure',
    },
    features: features,
  };
  content.push(featureListComponent);

  // 5. Rolex Rich Text Calibre component (empty content for new items)
  const richTextComponent = {
    _key: generateKey(),
    _type: 'rolexRichTextCalibre',
    alternativeBackgroundColor: false,
    content: [], // Empty for new items

    modelAvailability: true,
  };
  content.push(richTextComponent);

  // 6. Rolex Contact Us Accordion component
  const contactAccordionComponent = {
    _key: generateKey(),
    _type: 'rolexContactUsAccordionType',

    visible: true,
  };
  content.push(contactAccordionComponent);

  return content;
}

/**
 * Generate a random key for Sanity
 */
function generateKey() {
  return Math.random().toString(36).substring(2, 14);
}

/**
 * Generate rolexRichTextCalibre content from Excel data
 */
async function generateRolexRichTextCalibreContent(excelRow) {
  const content = [];

  console.log(`       🔍 Excel row keys: ${Object.keys(excelRow).join(', ')}`);

  // Process Feature1, Feature2, Feature3
  for (let i = 1; i <= 3; i++) {
    const titleField = `Feature${i}_Title`;
    const textField = `Feature${i}_Text`;
    const assetField = `Feature${i}_Asset`;

    const title = excelRow[titleField];
    const text = excelRow[textField];
    const assetPath = excelRow[assetField];

    console.log(`       📝 Feature${i}: Title="${title}", Text="${text}", Asset="${assetPath}"`);

    // Add Heading 1 block if title exists
    if (title && title.trim()) {
      content.push({
        _key: generateKey(),
        _type: 'block',
        children: [
          {
            _key: generateKey(),
            _type: 'span',
            marks: [],
            text: title.trim(),
          },
        ],
        markDefs: [],
        style: 'h1',
      });
    }

    // Add Normal text block if text exists
    if (text && text.trim()) {
      content.push({
        _key: generateKey(),
        _type: 'block',
        children: [
          {
            _key: generateKey(),
            _type: 'span',
            marks: [],
            text: text.trim(),
          },
        ],
        markDefs: [],
        style: 'normal',
      });
    }

    // Add InlineImage block if asset exists
    if (assetPath && assetPath.trim()) {
      try {
        // Try to find and upload the image
        const imagePath = findImageByAssetPath(assetPath.trim());
        if (imagePath) {
          const imageAsset = await uploadImageToSanity(imagePath, `feature${i}`, `Feature ${i} image`);
          if (imageAsset) {
            content.push({
              _key: generateKey(),
              _type: 'inlineImage',
              asset: imageAsset.asset,
              alt: ' ',
              widthRatio: '1/2',
              centerImage: true,
            });
            console.log(`       - Added Feature${i} image from Excel`);
          }
        } else {
          console.log(`       - Feature${i} image not found: ${assetPath}`);
        }
      } catch (error) {
        console.error(`       - Error processing Feature${i} image: ${error.message}`);
      }
    }
  }

  return content;
}

/**
 * Find image by asset path from Excel
 */
function findImageByAssetPath(assetPath) {
  if (!assetPath) return null;

  console.log(`   🔍 Looking for image: ${assetPath}`);

  // Split multiple paths by comma and try each one
  const paths = assetPath.split(',').map((p) => p.trim());

  for (const singlePath of paths) {
    console.log(`     🔍 Trying path: "${singlePath}"`);

    // Try different file extensions
    const extensions = ['.webp', '.jpg', '.jpeg', '.png'];

    for (const ext of extensions) {
      const fullPath = path.join('./scripts/Official\ Rolex\ Selection/retailer-selection_webp', singlePath + ext);
      console.log(`       📁 Checking: ${fullPath}`);

      if (fs.existsSync(fullPath)) {
        console.log(`   ✅ Found image: ${fullPath}`);
        return fullPath;
      }
    }
  }

  console.log(`   ❌ No image found for any path in: ${assetPath}`);
  return null;
}

/**
 * Find existing watch in Sanity by RMC
 */
async function findExistingWatch(rmc) {
  if (!rmc) return null;

  try {
    const query = `*[_type == "rolexWatch" && rmc == $rmc && defined(parentWatch)][0] {
      _id,
      title,
      slug,
      type,
      material,
      size,
      rmc,
      retailPrices,
      parentWatch,
      image,
      content[] {
        _type,
        _key,
        ...
      }
    }`;

    const result = await client.fetch(query, { rmc });
    return result;
  } catch (error) {
    console.error(`   ❌ Error finding existing watch: ${error.message}`);
    return null;
  }
}

/**
 * Find price data for a watch by RMC
 */
function findPriceData(rmc, pricesData) {
  if (!rmc || !pricesData || pricesData.length === 0) {
    return null;
  }

  // Try to find price data by RMC
  const priceRow = pricesData.find((row) => {
    if (!row.rmc) return false;
    return row.rmc.toString().toLowerCase() === rmc.toLowerCase();
  });

  if (priceRow) {
    console.log(`   💰 Found price data for RMC: ${rmc}`);
    return priceRow;
  }

  console.log(`   ⚠️  No price data found for RMC: ${rmc}`);
  return null;
}

/**
 * Check if image asset already exists
 */
function hasImageAsset(imageField) {
  return imageField && imageField.asset && imageField.asset._ref;
}

/**
 * Update existing watch in Sanity
 */
async function updateExistingWatch(existingWatch, excelRow, pricesData, dryRun = false) {
  try {
    console.log(`   🔄 Updating existing watch...`);

    // Find price data
    const priceData = findPriceData(excelRow.RMC, pricesData);

    // Prepare update data
    const updateData = {};

    // Update basic fields
    if (excelRow.ModelName && excelRow.ModelName !== existingWatch.title) {
      updateData.title = excelRow.ModelName;
    }

    if (excelRow.Spec_ModelCase && excelRow.Spec_ModelCase !== existingWatch.modelCase) {
      updateData.modelCase = cleanText(excelRow.Spec_ModelCase);
    }

    if (excelRow.Spec_Material && excelRow.Spec_Material !== existingWatch.material) {
      updateData.material = cleanText(excelRow.Spec_Material);
    }

    if (excelRow.Spec_Diameter) {
      const size = extractSize(excelRow.Spec_Diameter);
      if (size && size !== existingWatch.size) {
        updateData.size = size;
      }
    }

    if (excelRow.RMC && excelRow.RMC !== existingWatch.rmc) {
      updateData.rmc = cleanText(excelRow.RMC);
    }

    // Update retail prices
    if (priceData) {
      const priceFields = ['Rolex maximum suggested retail prices', 'Price', 'RetailPrice', 'SuggestedRetailPrice'];
      let priceValue = null;
      for (const field of priceFields) {
        if (priceData[field]) {
          priceValue = priceData[field];
          break;
        }
      }
      if (priceValue) {
        const formattedPrice = cleanText(priceValue.toString());
        if (formattedPrice !== existingWatch.retailPrices) {
          updateData.retailPrices = formattedPrice;
        }
      }
    }

    // Update parent watch reference
    if (excelRow.FamilyName) {
      const parentWatch = await findParentWatch(excelRow.FamilyName);
      if (parentWatch && (!existingWatch.parentWatch || existingWatch.parentWatch._ref !== parentWatch._ref)) {
        updateData.parentWatch = parentWatch;
      }
    }

    // Update slug if needed
    if (excelRow.FamilyName && excelRow.RMC) {
      const newSlug = generateSlug(excelRow.FamilyName, excelRow.RMC);
      if (newSlug && (!existingWatch.slug || existingWatch.slug.current !== newSlug)) {
        updateData.slug = {
          _type: 'slug',
          current: newSlug,
        };
      }
    }

    // Handle images only if they don't exist
    let heroImageAsset = null;
    let galleryImageAssets = [];

    if (!hasImageAsset(existingWatch.image)) {
      console.log(`   🖼️  Main image not found, uploading...`);
      const heroImagePath = findHeroImageForWatch(excelRow.RMC);
      if (heroImagePath) {
        heroImageAsset = await uploadImageToSanity(heroImagePath, excelRow.RMC, 'hero');
        if (heroImageAsset) {
          updateData.image = heroImageAsset;
        }
      }
    } else {
      console.log(`   ✅ Main image already exists, skipping upload`);
    }

    // Handle gallery images - only upload if needed
    const galleryImagePaths = findGalleryImagesForWatch(excelRow.RMC);
    if (galleryImagePaths.length > 0) {
      // Check if existing watch has gallery images
      const existingImageGrid = existingWatch.content?.find((item) => item._type === 'imageGridCols');
      const existingImages = existingImageGrid?.images || [];
      const hasAllGalleryImages = existingImages.length >= 4 && existingImages.every((img) => hasImageAsset(img.image));

      if (!hasAllGalleryImages) {
        console.log(`   🖼️  Gallery images missing or incomplete, uploading...`);
        for (let j = 0; j < galleryImagePaths.length; j++) {
          const imagePath = galleryImagePaths[j];
          const slideNumber = j + 1;
          const asset = await uploadImageToSanity(imagePath, excelRow.RMC, `gallery-${slideNumber}`);
          if (asset) {
            galleryImageAssets.push(asset);
          }
        }
      } else {
        console.log(`   ✅ Gallery images already exist, skipping upload`);
      }
    }

    // Handle content updates
    await updateWatchContent(existingWatch, excelRow, heroImageAsset, galleryImageAssets, updateData);

    // Show changes
    const changes = Object.keys(updateData);
    if (changes.length === 0) {
      console.log(`   ℹ️  No changes needed`);
      return true;
    }

    console.log(`   📋 Changes to be made: ${changes.join(', ')}`);

    if (dryRun) {
      console.log(`   🧪 DRY RUN - No actual changes made`);
      return true;
    }

    // Perform update
    const result = await client.patch(existingWatch._id).set(updateData).commit();
    console.log(`   ✅ Successfully updated watch: ${result._id}`);

    return true;
  } catch (error) {
    console.error(`   ❌ Error updating existing watch: ${error.message}`);
    return false;
  }
}

/**
 * Update watch content array
 */
async function updateWatchContent(existingWatch, excelRow, heroImageAsset, galleryImageAssets, updateData) {
  if (!existingWatch.content || !Array.isArray(existingWatch.content)) {
    console.log(`   ⚠️  No content array found in existing watch`);
    return;
  }

  console.log(`   🔍 Current content components: ${existingWatch.content.map((item) => item._type).join(', ')}`);

  const updatedContent = [...existingWatch.content];

  // Update rolexModelHero component
  const heroIndex = updatedContent.findIndex((item) => item._type === 'rolexModelHero');
  if (heroIndex !== -1) {
    const heroComponent = updatedContent[heroIndex];
    const heroComponentKey = heroComponent._key;

    // Update hero image if new one was uploaded and old one doesn't exist
    if (heroImageAsset && !hasImageAsset(heroComponent.image)) {
      updateData[`content[_key=="${heroComponentKey}"].image`] = heroImageAsset;
      console.log(`     - Updated hero image for rolexModelHero component`);
    }

    // Update hero content
    if (excelRow.ModelName && heroComponent.subheading !== excelRow.ModelName) {
      updateData[`content[_key=="${heroComponentKey}"].subheading`] = excelRow.ModelName;
      console.log(`     - Updated subheading for rolexModelHero component`);
    }

    // Always update body content (required field)
    const newBody = [
      {
        _key: generateKey(),
        _type: 'block',
        children: [
          {
            _key: generateKey(),
            _type: 'span',
            marks: [],
            text: excelRow.Spec_ModelCase || '',
          },
        ],
        markDefs: [],
        style: 'normal',
      },
      {
        _key: generateKey(),
        _type: 'block',
        children: [
          {
            _key: generateKey(),
            _type: 'span',
            marks: [],
            text: excelRow.RMC || '',
          },
        ],
        markDefs: [],
        style: 'normal',
      },
    ];
    updateData[`content[_key=="${heroComponentKey}"].body`] = newBody;
    console.log(`     - Updated body content for rolexModelHero component`);
  }

  // Update imageGridCols component
  const imageGridIndex = updatedContent.findIndex((item) => item._type === 'imageGridCols');
  if (imageGridIndex !== -1 && galleryImageAssets.length > 0) {
    const imageGridComponent = updatedContent[imageGridIndex];
    const imageGridComponentKey = imageGridComponent._key;

    // Check if any images are missing
    const existingImages = imageGridComponent.images || [];
    const needsUpdate = existingImages.length < 4 || existingImages.some((img) => !hasImageAsset(img.image));

    if (needsUpdate) {
      const newImages = galleryImageAssets.map((asset) => ({
        _key: generateKey(),
        alt: ' ',
        image: asset,
      }));
      updateData[`content[_key=="${imageGridComponentKey}"].images`] = newImages;
      updateData[`content[_key=="${imageGridComponentKey}"].columns`] = 4;
      updateData[`content[_key=="${imageGridComponentKey}"].spacing`] = 4;
      console.log(`     - Updated gallery images for imageGridCols component`);
    }
  }

  // Handle richText, rolexRichText, and rolexRichTextCalibre components
  console.log(`   🔄 Checking for richText/rolexRichText/rolexRichTextCalibre components...`);
  let richTextReplacements = 0;

  for (let i = 0; i < updatedContent.length; i++) {
    if (
      updatedContent[i]._type === 'rolexRichText' ||
      updatedContent[i]._type === 'richText' ||
      updatedContent[i]._type === 'rolexRichTextCalibre'
    ) {
      const oldType = updatedContent[i]._type;
      const componentKey = updatedContent[i]._key;

      if (oldType === 'rolexRichTextCalibre') {
        console.log(`   ✅ Found ${oldType} component at index ${i} (key: ${componentKey}), updating content`);
      } else {
        console.log(
          `   ✅ Found ${oldType} component at index ${i} (key: ${componentKey}), upgrading to rolexRichTextCalibre`
        );
        // Update using specific path to preserve _key
        updateData[`content[_key=="${componentKey}"]._type`] = 'rolexRichTextCalibre';
      }

      // Ensure modelAvailability is set
      if (!updatedContent[i].modelAvailability) {
        updateData[`content[_key=="${componentKey}"].modelAvailability`] = true;
      }

      // Generate new content from Excel data for rolexRichTextCalibre
      console.log(`     - Generating new content from Excel Feature data...`);
      const newRichTextContent = await generateRolexRichTextCalibreContent(excelRow);
      if (newRichTextContent && newRichTextContent.length > 0) {
        updateData[`content[_key=="${componentKey}"].content`] = newRichTextContent;
        console.log(`     - Generated new content from Excel data (${newRichTextContent.length} blocks)`);
      } else {
        console.log(`     - No Excel Feature data available, clearing content`);
        // Clear content if no Excel data available
        updateData[`content[_key=="${componentKey}"].content`] = [];
      }

      richTextReplacements++;
    }
  }

  if (richTextReplacements > 0) {
    console.log(`   ✅ Upgraded ${richTextReplacements} richText/rolexRichText component(s) to rolexRichTextCalibre`);
  } else {
    console.log(`   ℹ️  No richText/rolexRichText components found to upgrade`);
  }

  // Note: All content updates are now handled via path-based updates in updateData
  // No need to update the entire content array
}

/**
 * Create new watch in Sanity
 */
async function createNewWatch(excelRow, pricesData, dryRun = false) {
  try {
    console.log(`   ➕ Creating new watch...`);

    if (!excelRow.RMC || !excelRow.ModelName) {
      console.log(`   ❌ Missing required fields (RMC or ModelName)`);
      return false;
    }

    // Find price data
    const priceData = findPriceData(excelRow.RMC, pricesData);

    // Upload images
    let heroImageAsset = null;
    let galleryImageAssets = [];

    // Upload hero image
    const heroImagePath = findHeroImageForWatch(excelRow.RMC);
    if (heroImagePath) {
      heroImageAsset = await uploadImageToSanity(heroImagePath, excelRow.RMC, 'hero');
    }

    // Upload gallery images
    const galleryImagePaths = findGalleryImagesForWatch(excelRow.RMC);
    if (galleryImagePaths.length > 0) {
      for (let j = 0; j < galleryImagePaths.length; j++) {
        const imagePath = galleryImagePaths[j];
        const slideNumber = j + 1;
        const asset = await uploadImageToSanity(imagePath, excelRow.RMC, `gallery-${slideNumber}`);
        if (asset) {
          galleryImageAssets.push(asset);
        }
      }
    }

    // Generate content array
    const content = await generateWatchContent(excelRow, heroImageAsset, galleryImageAssets);

    // Find parent watch
    const parentWatch = await findParentWatch(excelRow.FamilyName);

    // Generate slug
    const slug = generateSlug(excelRow.FamilyName, excelRow.RMC);

    // Prepare retail prices
    let retailPrices = null;
    if (priceData) {
      const priceFields = ['Rolex maximum suggested retail prices', 'Price', 'RetailPrice', 'SuggestedRetailPrice'];
      for (const field of priceFields) {
        if (priceData[field]) {
          retailPrices = cleanText(priceData[field].toString());
          break;
        }
      }
    }

    // Create new watch document
    const newWatch = {
      _type: 'rolexWatch',
      title: excelRow.ModelName,
      rmc: cleanText(excelRow.RMC),
      modelCase: excelRow.Spec_ModelCase ? cleanText(excelRow.Spec_ModelCase) : null,
      material: excelRow.Spec_Material ? cleanText(excelRow.Spec_Material) : null,
      size: excelRow.Spec_Diameter ? extractSize(excelRow.Spec_Diameter) : null,
      retailPrices: retailPrices,
      releaseDate: new Date().toISOString().split('T')[0], // Current date
      slug: slug
        ? {
            _type: 'slug',
            current: slug,
          }
        : null,
      image: heroImageAsset,
      content: content,
      parentWatch: parentWatch,
    };

    console.log(`   📋 New watch data prepared:`);
    console.log(`      Title: ${newWatch.title}`);
    console.log(`      RMC: ${newWatch.rmc}`);
    console.log(`      Slug: ${newWatch.slug?.current || 'Not set'}`);
    console.log(`      Parent: ${parentWatch ? 'Found' : 'Not found'}`);
    console.log(`      Images: Hero=${heroImageAsset ? 'Yes' : 'No'}, Gallery=${galleryImageAssets.length}`);

    if (dryRun) {
      console.log(`   🧪 DRY RUN - No actual creation performed`);
      return true;
    }

    // Create the document
    const result = await client.create(newWatch);
    console.log(`   ✅ Successfully created new watch: ${result._id}`);

    return true;
  } catch (error) {
    console.error(`   ❌ Error creating new watch: ${error.message}`);
    return false;
  }
}

/**
 * Upgrade all richText and rolexRichText components to rolexRichTextCalibre globally
 */
async function upgradeAllRolexRichTextComponents(dryRun = false) {
  try {
    console.log('\n🔄 Checking for richText/rolexRichText components to upgrade globally...');

    const query = `*[_type == "rolexWatch" && defined(parentWatch) && (content[_type == "rolexRichText"] || content[_type == "richText"])] {
      _id,
      title,
      content[] {
        _type,
        _key,
        ...
      }
    }`;

    const watchesWithOldComponents = await client.fetch(query);

    if (watchesWithOldComponents.length === 0) {
      console.log('✅ No richText/rolexRichText components found to upgrade');
      return 0;
    }

    console.log(`🔍 Found ${watchesWithOldComponents.length} watches with richText/rolexRichText components`);

    let totalUpgraded = 0;

    for (const watch of watchesWithOldComponents) {
      let componentCount = 0;
      const patchOperations = {};

      for (let i = 0; i < watch.content.length; i++) {
        const component = watch.content[i];
        if (component._type === 'rolexRichText' || component._type === 'richText') {
          const oldType = component._type;
          const componentKey = component._key;

          console.log(`     - Upgrading ${oldType} to rolexRichTextCalibre (key: ${componentKey})`);

          // Use path-based updates to preserve _key
          patchOperations[`content[_key=="${componentKey}"]._type`] = 'rolexRichTextCalibre';

          if (!component.modelAvailability) {
            patchOperations[`content[_key=="${componentKey}"].modelAvailability`] = true;
          }

          // Clear content for rolexRichTextCalibre (will be populated from Excel data during individual updates)
          console.log(`       - Clearing content for rolexRichTextCalibre (to be populated from Excel data)`);
          patchOperations[`content[_key=="${componentKey}"].content`] = [];

          componentCount++;
        }
      }

      if (componentCount > 0) {
        console.log(`   ✅ Upgrading ${componentCount} component(s) in: ${watch.title}`);

        if (!dryRun) {
          await client.patch(watch._id).set(patchOperations).commit();
        }

        totalUpgraded += componentCount;
      }
    }

    console.log(`✅ Total richText/rolexRichText components upgraded: ${totalUpgraded}`);
    return totalUpgraded;
  } catch (error) {
    console.error('❌ Error upgrading richText/rolexRichText components:', error.message);
    return 0;
  }
}

/**
 * Main execution function
 */
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');

  // Check for RMC array mode
  const rmcArg = args.find((arg) => arg.startsWith('--rmc='));
  let targetRmcs = null;

  if (rmcArg) {
    const rmcString = rmcArg.split('=')[1];
    if (rmcString) {
      targetRmcs = rmcString
        .split(',')
        .map((rmc) => rmc.trim())
        .filter((rmc) => rmc.length > 0);
    }
  }

  console.log('🔧 Rolex Watch Update Script (Excel-First Mode)');
  console.log('===============================================');

  if (targetRmcs && targetRmcs.length > 0) {
    console.log('🎯 Processing specific RMC codes:');
    targetRmcs.forEach((rmc, index) => {
      console.log(`   ${index + 1}. ${rmc}`);
    });
  } else {
    console.log('📋 Processing all watches from Excel data');
  }

  // Validate Excel files exist
  if (!fs.existsSync(SPECS_FILE)) {
    console.error(`❌ Specifications file not found: ${SPECS_FILE}`);
    process.exit(1);
  }

  if (!fs.existsSync(PRICES_FILE)) {
    console.warn(`⚠️  Pricing file not found: ${PRICES_FILE}`);
  }

  if (!fs.existsSync(HERO_IMAGES_DIR)) {
    console.warn(`⚠️  Hero images directory not found: ${HERO_IMAGES_DIR}`);
    console.warn(`   Hero image updates will be skipped`);
  }

  if (!fs.existsSync(GALLERY_IMAGES_DIR)) {
    console.warn(`⚠️  Gallery images directory not found: ${GALLERY_IMAGES_DIR}`);
    console.warn(`   Gallery image updates will be skipped`);
  }

  // Read Excel data
  console.log(`\n📊 Reading Excel files...`);
  const excelData = readExcelFile(SPECS_FILE, 'Watches List');
  const pricesData = fs.existsSync(PRICES_FILE) ? readExcelFile(PRICES_FILE, 'Suggested Retail PricesGB') : [];

  if (excelData.length === 0) {
    console.error('❌ No data found in specifications Excel file');
    process.exit(1);
  }

  console.log(`✅ Found ${excelData.length} records in specifications Excel`);
  console.log(`✅ Found ${pricesData.length} records in pricing Excel`);

  // Filter Excel data if specific RMCs are requested
  let filteredExcelData = excelData;
  if (targetRmcs && targetRmcs.length > 0) {
    filteredExcelData = excelData.filter(
      (row) => row.RMC && targetRmcs.some((rmc) => rmc.toLowerCase() === row.RMC.toString().toLowerCase())
    );

    if (filteredExcelData.length === 0) {
      console.log('ℹ️  No Excel records found with the specified RMC codes');
      console.log('💡 Make sure the RMC codes exist in the Excel file');
      process.exit(0);
    }

    console.log(`✅ Filtered to ${filteredExcelData.length} Excel records`);
  }

  // Step 1: Upgrade all rolexRichText components globally
  const upgradedComponents = await upgradeAllRolexRichTextComponents(dryRun);

  let successCount = 0;
  let failureCount = 0;
  let noChangesCount = 0;
  let createdCount = 0;

  // Step 2: Process each Excel row
  for (let i = 0; i < filteredExcelData.length; i++) {
    const excelRow = filteredExcelData[i];
    const rmc = excelRow.RMC;

    if (!rmc) {
      console.log(`\n[${i + 1}/${filteredExcelData.length}] Skipping row without RMC`);
      failureCount++;
      continue;
    }

    console.log(`\n[${i + 1}/${filteredExcelData.length}] Processing Excel row: ${excelRow.ModelName || 'Unknown'}`);
    console.log(`   RMC: ${rmc}`);

    try {
      // Check if watch exists in Sanity
      const existingWatch = await findExistingWatch(rmc);

      if (existingWatch) {
        console.log(`   ✅ Found existing watch in Sanity: ${existingWatch.title}`);
        console.log(`   ID: ${existingWatch._id}`);

        // Update existing watch
        const success = await updateExistingWatch(existingWatch, excelRow, pricesData, dryRun);
        if (success) {
          successCount++;
        } else {
          failureCount++;
        }
      } else {
        console.log(`   ➕ Watch not found in Sanity, creating new watch`);

        // Create new watch
        const success = await createNewWatch(excelRow, pricesData, dryRun);
        if (success) {
          createdCount++;
        } else {
          failureCount++;
        }
      }
    } catch (error) {
      console.error(`   ❌ Error processing Excel row: ${error.message}`);
      failureCount++;
    }
  }

  // Summary
  console.log('\n📊 Processing Summary:');
  console.log(`   ✅ Successfully updated: ${successCount}`);
  console.log(`   ➕ Successfully created: ${createdCount}`);
  console.log(`   🔄 Components upgraded: ${upgradedComponents}`);
  console.log(`   ❌ Failed: ${failureCount}`);
  console.log(`   ℹ️  No changes needed: ${noChangesCount}`);
  console.log(`   📋 Total Excel records: ${filteredExcelData.length}`);

  if (dryRun) {
    console.log('\n💡 To actually perform the updates, add --execute flag:');
    if (targetRmcs && targetRmcs.length > 0) {
      console.log(`  node scripts/populate-rolex-watches-new.js --rmc=${targetRmcs.join(',')} --execute`);
    } else {
      console.log('  node scripts/populate-rolex-watches-new.js --execute');
    }
    console.log('\n📚 Usage examples:');
    console.log('  # Update all watches:');
    console.log('  node scripts/populate-rolex-watches-new.js --execute');
    console.log('  # Update specific RMC codes:');
    console.log('  node scripts/populate-rolex-watches-new.js --rmc=M126610LN-0001,M126505-0001 --execute');
  } else {
    console.log('\n🎉 Batch update completed!');
  }
}

// Run the script
if (decodeURIComponent(import.meta.url) === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
