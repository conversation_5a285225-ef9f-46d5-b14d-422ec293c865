#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify if a document has been deleted from Sanity
 * This script checks multiple perspectives to ensure the document is truly gone
 */

import { createClient } from '@sanity/client';
import dotenv from 'dotenv';

// Target document ID
const TARGET_DOCUMENT_ID = '696cba8c-0faf-4d5b-b521-12e1d13be3dd';

// Load environment variables
try {
  dotenv.config({ path: '.env.local' });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
} catch (e) {
  // Continue with process.env
}

// Validate environment variables
const requiredEnvVars = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN,
};

function validateEnvironment() {
  const missing = Object.entries(requiredEnvVars)
    .filter(([, value]) => !value)
    .map(([key]) => key);

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach((key) => {
      const envVar =
        key === 'projectId'
          ? 'NEXT_PUBLIC_SANITY_PROJECT_ID'
          : key === 'dataset'
            ? 'NEXT_PUBLIC_SANITY_DATASET'
            : 'SANITY_API_TOKEN';
      console.error(`  - ${envVar}`);
    });
    process.exit(1);
  }
}

validateEnvironment();

// Create multiple clients with different perspectives
const clients = {
  published: createClient({
    projectId: requiredEnvVars.projectId,
    dataset: requiredEnvVars.dataset,
    useCdn: false,
    token: requiredEnvVars.token,
    apiVersion: '2024-01-01',
    perspective: 'published',
  }),
  drafts: createClient({
    projectId: requiredEnvVars.projectId,
    dataset: requiredEnvVars.dataset,
    useCdn: false,
    token: requiredEnvVars.token,
    apiVersion: '2024-01-01',
    perspective: 'drafts',
  }),
  raw: createClient({
    projectId: requiredEnvVars.projectId,
    dataset: requiredEnvVars.dataset,
    useCdn: false,
    token: requiredEnvVars.token,
    apiVersion: '2024-01-01',
    perspective: 'raw',
  }),
};

async function checkDocument(clientName, client) {
  try {
    console.log(`\n🔍 Checking ${clientName} perspective...`);

    const query = `*[_id == $id][0]`;
    const params = { id: TARGET_DOCUMENT_ID };

    const document = await client.fetch(query, params);

    if (document) {
      console.log(`❌ Document STILL EXISTS in ${clientName}:`);
      console.log(`   ID: ${document._id}`);
      console.log(`   Type: ${document._type}`);
      console.log(`   Title: ${document.title || 'N/A'}`);
      console.log(`   Created: ${document._createdAt}`);
      console.log(`   Updated: ${document._updatedAt}`);
      return true;
    } else {
      console.log(`✅ Document NOT FOUND in ${clientName} - Successfully deleted`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error checking ${clientName}:`, error.message);
    return false;
  }
}

async function checkHistory() {
  try {
    console.log(`\n🕒 Checking document history...`);

    // Check if document exists in history
    const historyQuery = `*[_id == $id || _id == $draftId] | order(_updatedAt desc)`;
    const params = {
      id: TARGET_DOCUMENT_ID,
      draftId: `drafts.${TARGET_DOCUMENT_ID}`,
    };

    const history = await clients.raw.fetch(historyQuery, params);

    if (history && history.length > 0) {
      console.log(`⚠️  Found ${history.length} document(s) in history:`);
      history.forEach((doc, index) => {
        console.log(`   ${index + 1}. ID: ${doc._id} | Type: ${doc._type} | Updated: ${doc._updatedAt}`);
      });
      return true;
    } else {
      console.log(`✅ No documents found in history`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error checking history:`, error.message);
    return false;
  }
}

async function forceDelete() {
  try {
    console.log(`\n🔥 Attempting force deletion...`);

    // Try to delete both the document and its draft
    const deletePromises = [clients.raw.delete(TARGET_DOCUMENT_ID), clients.raw.delete(`drafts.${TARGET_DOCUMENT_ID}`)];

    const results = await Promise.allSettled(deletePromises);

    results.forEach((result, index) => {
      const docType = index === 0 ? 'published' : 'draft';
      if (result.status === 'fulfilled') {
        console.log(`✅ Successfully deleted ${docType} version`);
        console.log(`   Transaction ID: ${result.value.transactionId}`);
      } else {
        console.log(`ℹ️  ${docType} version not found or already deleted`);
      }
    });

    return true;
  } catch (error) {
    console.error(`❌ Error during force deletion:`, error.message);
    return false;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const forceDeleteFlag = args.includes('--force-delete');

  console.log('🔍 Document Deletion Verification Script');
  console.log('========================================');
  console.log(`🎯 Target Document ID: ${TARGET_DOCUMENT_ID}`);

  let documentExists = false;

  // Check all perspectives
  for (const [name, client] of Object.entries(clients)) {
    const exists = await checkDocument(name, client);
    if (exists) documentExists = true;
  }

  // Check history
  const existsInHistory = await checkHistory();

  // Summary
  console.log('\n📊 Summary:');
  if (documentExists) {
    console.log('❌ Document still exists in one or more perspectives');

    if (forceDeleteFlag) {
      console.log('\n🔥 Force deletion requested...');
      await forceDelete();

      // Re-check after force deletion
      console.log('\n🔄 Re-checking after force deletion...');
      let stillExists = false;
      for (const [name, client] of Object.entries(clients)) {
        const exists = await checkDocument(name, client);
        if (exists) stillExists = true;
      }

      if (!stillExists) {
        console.log('\n🎉 Document successfully deleted!');
      } else {
        console.log('\n❌ Document still exists after force deletion');
      }
    } else {
      console.log('\n💡 To force delete the document, run:');
      console.log('  node scripts/verify-deletion.js --force-delete');
    }
  } else {
    console.log('✅ Document successfully deleted from all perspectives');
    if (existsInHistory) {
      console.log('ℹ️  Document still exists in history (this is normal)');
    }
  }
}

main().catch(console.error);
