import { createClient } from '@sanity/client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Sanity client with multiple perspectives
const clients = {
  published: createClient({
    projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
    dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
    token: process.env.SANITY_API_TOKEN,
    useCdn: false,
    apiVersion: '2024-01-01',
    perspective: 'published',
  }),
  drafts: createClient({
    projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
    dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
    token: process.env.SANITY_API_TOKEN,
    useCdn: false,
    apiVersion: '2024-01-01',
    perspective: 'drafts',
  }),
  raw: createClient({
    projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
    dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
    token: process.env.SANITY_API_TOKEN,
    useCdn: false,
    apiVersion: '2024-01-01',
    perspective: 'raw',
  }),
};

/**
 * Find watches with empty RMC or Model Case
 */
async function findIncompleteWatches() {
  console.log('🔍 Finding watches with empty RMC or Model Case...');

  try {
    const query = `*[_type == "rolexWatch" && defined(parentWatch) && (
      !defined(rmc) || 
      rmc == "" || 
      !defined(modelCase) || 
      modelCase == ""
    )] {
      _id,
      title,
      rmc,
      modelCase,
      slug
    }`;

    const watches = await clients.raw.fetch(query);

    console.log(`\n📊 Found ${watches.length} incomplete watches:`);

    watches.forEach((watch, index) => {
      console.log(`\n${index + 1}. ${watch.title || 'Untitled'}`);
      console.log(`   ID: ${watch._id}`);
      console.log(`   RMC: ${watch.rmc || '❌ EMPTY'}`);
      console.log(`   Model Case: ${watch.modelCase || '❌ EMPTY'}`);
      console.log(`   Slug: ${watch.slug?.current || 'No slug'}`);
    });

    return watches;
  } catch (error) {
    console.error('❌ Error finding incomplete watches:', error.message);
    return [];
  }
}

/**
 * Delete watches by IDs
 */
async function deleteWatches(watchIds, dryRun = true) {
  if (watchIds.length === 0) {
    console.log('\n✅ No watches to delete.');
    return;
  }

  console.log(`\n${dryRun ? '🔍 DRY RUN:' : '🗑️ DELETING:'} ${watchIds.length} watches...`);

  if (dryRun) {
    console.log('   This is a dry run. Use --execute to actually delete the watches.');
    return;
  }

  try {
    // Delete watches in batches with force deletion
    const batchSize = 5; // Smaller batches for more reliable deletion
    let deletedCount = 0;
    let failedDeletions = [];

    for (let i = 0; i < watchIds.length; i += batchSize) {
      const batch = watchIds.slice(i, i + batchSize);

      console.log(`   Deleting batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(watchIds.length / batchSize)}...`);

      for (const id of batch) {
        try {
          // Try to delete both published and draft versions
          const deletePromises = [clients.raw.delete(id), clients.raw.delete(`drafts.${id}`)];

          const results = await Promise.allSettled(deletePromises);

          let deleted = false;
          results.forEach((result, index) => {
            const docType = index === 0 ? 'published' : 'draft';
            if (result.status === 'fulfilled') {
              console.log(`     ✅ Deleted ${docType} version of ${id}`);
              deleted = true;
            }
          });

          if (deleted) {
            deletedCount++;
          } else {
            failedDeletions.push(id);
            console.log(`     ⚠️  Failed to delete ${id}`);
          }
        } catch (error) {
          failedDeletions.push(id);
          console.log(`     ❌ Error deleting ${id}: ${error.message}`);
        }
      }
    }

    console.log(`\n✅ Successfully deleted ${deletedCount} watches.`);
    if (failedDeletions.length > 0) {
      console.log(`❌ Failed to delete ${failedDeletions.length} watches:`);
      failedDeletions.forEach((id) => console.log(`   - ${id}`));
    }
  } catch (error) {
    console.error('❌ Error deleting watches:', error.message);
  }
}

/**
 * Verify deletion by checking all perspectives
 */
async function verifyDeletion(watchIds) {
  console.log('\n🔍 Verifying deletion...');

  let stillExisting = [];

  for (const id of watchIds) {
    // Check all perspectives
    for (const [perspective, client] of Object.entries(clients)) {
      try {
        const doc = await client.fetch(`*[_id == $id][0]`, { id });
        if (doc) {
          stillExisting.push({ id, perspective, title: doc.title });
        }
      } catch {
        // Ignore errors - document might not exist
      }
    }
  }

  if (stillExisting.length === 0) {
    console.log('✅ All documents successfully deleted from all perspectives');
  } else {
    console.log(`❌ ${stillExisting.length} documents still exist:`);
    stillExisting.forEach(({ id, perspective, title }) => {
      console.log(`   - ${id} (${title}) in ${perspective}`);
    });
  }

  return stillExisting;
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  const confirmDelete = args.includes('--confirm');

  console.log('🧹 Rolex Watch Cleanup Script');
  console.log('===============================');

  if (dryRun) {
    console.log('🔍 Running in DRY RUN mode - no changes will be made');
  } else {
    console.log('⚠️  EXECUTE mode - watches will be permanently deleted!');
  }

  // Find incomplete watches
  const incompleteWatches = await findIncompleteWatches();

  if (incompleteWatches.length === 0) {
    console.log('\n✅ No incomplete watches found. All watches have RMC and Model Case data.');
    return;
  }

  // Show summary
  console.log('\n📋 Summary of issues:');
  const emptyRmc = incompleteWatches.filter((w) => !w.rmc || w.rmc === '');
  const emptyModelCase = incompleteWatches.filter((w) => !w.modelCase || w.modelCase === '');
  const emptyBoth = incompleteWatches.filter((w) => (!w.rmc || w.rmc === '') && (!w.modelCase || w.modelCase === ''));

  console.log(`   Empty RMC: ${emptyRmc.length} watches`);
  console.log(`   Empty Model Case: ${emptyModelCase.length} watches`);
  console.log(`   Empty both: ${emptyBoth.length} watches`);

  if (!dryRun && !confirmDelete) {
    console.log('\n⚠️  To proceed with deletion, add --confirm flag');
    console.log('   Example: node scripts/delete-incomplete-watches.js --execute --confirm');
    return;
  }

  // Delete watches
  const watchIds = incompleteWatches.map((w) => w._id);
  await deleteWatches(watchIds, dryRun);

  // Verify deletion if not dry run
  if (!dryRun) {
    await verifyDeletion(watchIds);
  }

  if (dryRun) {
    console.log('\n💡 Usage options:');
    console.log('  --execute              Actually delete the watches');
    console.log('  --confirm              Required confirmation for deletion');
    console.log('\nExample: node scripts/delete-incomplete-watches.js --execute --confirm');
  }
}

// Run the script
main().catch(console.error);

export { findIncompleteWatches, deleteWatches };
