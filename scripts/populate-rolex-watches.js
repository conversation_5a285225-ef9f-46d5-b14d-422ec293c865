#!/usr/bin/env node

/**
 * <PERSON>ript to populate Rolex watches in Sanity from Excel data
 *
 * Usage: node scripts/populate-rolex-watches.js
 *
 * This script reads the Rolex Excel files and creates/updates watch entries in Sanity
 */

import XLSX from 'xlsx';
import { createClient } from '@sanity/client';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables from .env.local if available
try {
  dotenv.config({ path: '.env.local' });
} catch (e) {
  // dotenv not available, continue with process.env
}

// Validate required environment variables
const requiredEnvVars = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN,
};

function validateEnvironment() {
  const missing = Object.entries(requiredEnvVars)
    .filter(([key, value]) => !value)
    .map(([key]) => key);

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach((key) => {
      const envVar =
        key === 'projectId'
          ? 'NEXT_PUBLIC_SANITY_PROJECT_ID'
          : key === 'dataset'
            ? 'NEXT_PUBLIC_SANITY_DATASET'
            : 'SANITY_API_TOKEN';
      console.error(`  - ${envVar}`);
    });
    console.error('\nPlease set these in your .env.local file or environment.');
    process.exit(1);
  }
}

// Sanity client configuration
let client;
try {
  validateEnvironment();
  client = createClient({
    projectId: requiredEnvVars.projectId,
    dataset: requiredEnvVars.dataset,
    useCdn: false,
    token: requiredEnvVars.token,
    apiVersion: '2024-01-01',
  });
} catch (error) {
  console.error('❌ Failed to initialize Sanity client:', error.message);
  process.exit(1);
}

// File paths
const DATA_DIR = './scripts/Official\ Rolex\ Selection';
const SPECS_FILE = path.join(DATA_DIR, 'technical_specifications_rolex_selection_en.xlsx');
const PRICES_FILE = path.join(DATA_DIR, 'RolexMaximumSuggestedRetailPrices_GB.xlsx');
const IMAGES_DIR = '/tmp/rolex-images/retailer-selection_webp';

// Category mapping based on Rolex families
const CATEGORY_MAPPING = {
  // Professional watches
  Submariner: 'professional',
  'Sea-Dweller': 'professional',
  Deepsea: 'professional',
  'GMT-Master': 'professional',
  Explorer: 'professional',
  'Cosmograph Daytona': 'professional',
  'Yacht-Master': 'professional',
  Milgauss: 'professional',
  'Air-King': 'professional',

  // Classic watches
  Datejust: 'classic',
  'Lady-Datejust': 'classic',
  'Oyster Perpetual': 'classic',

  // Perpetual watches
  'Day-Date': 'perpetual',
  'Sky-Dweller': 'perpetual',
  Cellini: 'perpetual',
};

/**
 * Generate a unique key
 */
function generateUniqueKey(prefix = '') {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Read and parse Excel file
 */
function readExcelFile(filePath, sheetName = null) {
  try {
    const workbook = XLSX.readFile(filePath);
    const sheet = sheetName || workbook.SheetNames[0];
    return XLSX.utils.sheet_to_json(workbook.Sheets[sheet]);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return [];
  }
}

/**
 * Clean and format text
 */
function cleanText(text) {
  if (!text) return '';
  return text.toString().trim().replace(/\s+/g, ' ').replace(/ /g, '\n');
}

/**
 * Extract size from diameter specification
 */
function extractSize(diameter) {
  if (!diameter) return null;
  const match = diameter.toString().match(/(\d+(?:\.\d+)?)/);
  return match ? parseFloat(match[1]) : null;
}

/**
 * Upload image to Sanity and return asset reference
 */
async function uploadImageToSanity(imagePath, watchRef) {
  try {
    if (!fs.existsSync(imagePath)) {
      console.warn(`⚠️  Image not found: ${imagePath}`);
      return null;
    }

    const imageBuffer = fs.readFileSync(imagePath);
    const filename = path.basename(imagePath);

    console.log(`  📸 Uploading image: ${filename}`);

    const asset = await client.assets.upload('image', imageBuffer, {
      filename,
      title: `${watchRef} - ${filename}`,
    });

    return {
      _type: 'image',
      _key: generateUniqueKey('image'), // Add unique key
      asset: {
        _type: 'reference',
        _ref: asset._id,
      },
      alt: `${watchRef} watch image`,
    };
  } catch (error) {
    console.error(`Error uploading image ${imagePath}:`, error.message);
    return null;
  }
}

/**
 * Find feature asset image
 */
async function findFeatureAssetImage(assetName, watchRef, dryRun = false) {
  if (!assetName || dryRun) return null;

  // Clean the asset name for file matching
  const cleanAssetName = assetName.toLowerCase().replace(/[^a-z0-9-]/g, '');
  const cleanRef = watchRef.toLowerCase().replace(/[^a-z0-9-]/g, '');

  // Look in feature directories
  const featurePortraitDir = path.join(IMAGES_DIR, 'model_feature_assets_portrait');
  const featureLandscapeDir = path.join(IMAGES_DIR, 'model_feature_assets_landscape');

  for (const dir of [featurePortraitDir, featureLandscapeDir]) {
    if (fs.existsSync(dir)) {
      const subDirs = fs
        .readdirSync(dir, { withFileTypes: true })
        .filter((dirent) => dirent.isDirectory())
        .map((dirent) => dirent.name);

      for (const subDir of subDirs) {
        // Try multiple naming patterns
        const possibleNames = [
          `${cleanRef}_${cleanAssetName}.webp`,
          `${cleanRef}-${cleanAssetName}.webp`,
          `${cleanAssetName}.webp`,
          `${cleanRef}.webp`,
        ];

        for (const possibleName of possibleNames) {
          const imagePath = path.join(dir, subDir, possibleName);
          if (fs.existsSync(imagePath)) {
            return await uploadImageToSanity(imagePath, watchRef);
          }
        }
      }
    }
  }

  return null;
}

/**
 * Find and upload images for a watch
 */
async function findAndUploadWatchImages(watchRef, dryRun = false) {
  const images = {
    heroImage: null,
    galleryImages: [],
    featureImages: [],
  };

  if (dryRun) {
    // In dry run, just report what images would be found
    const cleanRef = watchRef.toLowerCase().replace(/[^a-z0-9-]/g, '');

    const uprightPortraitPath = path.join(IMAGES_DIR, 'upright_watch_assets_portrait', `${cleanRef}.webp`);
    const uprightLandscapePath = path.join(IMAGES_DIR, 'upright_watch_assets_landscape', `${cleanRef}.webp`);

    if (fs.existsSync(uprightPortraitPath)) {
      console.log(`  📸 Would upload hero image: upright_watch_assets_portrait/${cleanRef}.webp`);
    } else if (fs.existsSync(uprightLandscapePath)) {
      console.log(`  📸 Would upload hero image: upright_watch_assets_landscape/${cleanRef}.webp`);
    } else {
      console.log(`  ⚠️  No hero image found for: ${cleanRef}`);
    }

    return images;
  }

  // Clean reference number for filename matching (images are named with 'm' prefix)
  const cleanRef = watchRef.toLowerCase().replace(/[^a-z0-9-]/g, '');

  try {
    // Main hero/upright image
    const uprightPortraitPath = path.join(IMAGES_DIR, 'upright_watch_assets_portrait', `${cleanRef}.webp`);
    const uprightLandscapePath = path.join(IMAGES_DIR, 'upright_watch_assets_landscape', `${cleanRef}.webp`);

    if (fs.existsSync(uprightPortraitPath)) {
      images.heroImage = await uploadImageToSanity(uprightPortraitPath, watchRef);
    } else if (fs.existsSync(uprightLandscapePath)) {
      images.heroImage = await uploadImageToSanity(uprightLandscapePath, watchRef);
    }

    // Gallery images
    const galleryPortraitDir = path.join(IMAGES_DIR, 'model_gallery_assets_portrait');
    const galleryLandscapeDir = path.join(IMAGES_DIR, 'model_gallery_assets_landscape');

    for (const dir of [galleryPortraitDir, galleryLandscapeDir]) {
      if (fs.existsSync(dir)) {
        const subDirs = fs
          .readdirSync(dir, { withFileTypes: true })
          .filter((dirent) => dirent.isDirectory())
          .map((dirent) => dirent.name);

        for (const subDir of subDirs) {
          const imagePath = path.join(dir, subDir, `${cleanRef}.webp`);
          if (fs.existsSync(imagePath)) {
            const uploadedImage = await uploadImageToSanity(imagePath, watchRef);
            if (uploadedImage) {
              images.galleryImages.push(uploadedImage);
            }
          }
        }
      }
    }

    // Feature images
    const featurePortraitDir = path.join(IMAGES_DIR, 'model_feature_assets_portrait');
    const featureLandscapeDir = path.join(IMAGES_DIR, 'model_feature_assets_landscape');

    for (const dir of [featurePortraitDir, featureLandscapeDir]) {
      if (fs.existsSync(dir)) {
        const subDirs = fs
          .readdirSync(dir, { withFileTypes: true })
          .filter((dirent) => dirent.isDirectory())
          .map((dirent) => dirent.name);

        for (const subDir of subDirs) {
          const imagePath = path.join(dir, subDir, `${cleanRef}.webp`);
          if (fs.existsSync(imagePath)) {
            const uploadedImage = await uploadImageToSanity(imagePath, watchRef);
            if (uploadedImage) {
              images.featureImages.push(uploadedImage);
            }
          }
        }
      }
    }
  } catch (error) {
    console.error(`Error finding images for ${watchRef}:`, error.message);
  }

  return images;
}

/**
 * Generate slug from title and unique identifier
 */
function generateSlug(title, rmc = null, releaseDate = null) {
  let baseSlug = title
    .toLowerCase()
    .replace(/rolex\s+/g, '')
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');

  // Add RMC suffix for uniqueness if available
  if (rmc) {
    const cleanRmc = rmc
      .toLowerCase()
      .replace(/[^\w-]/g, '')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
    baseSlug = `${baseSlug}-${cleanRmc}`;
  }

  // Check if it's a new watch (released after August of current year)
  if (releaseDate) {
    const currentYear = new Date().getFullYear();
    const cutoffDate = new Date(`${currentYear}-08-31`);
    const release = new Date(releaseDate);

    if (release > cutoffDate) {
      return `/rolex/new-watches/${baseSlug}`;
    }
  }

  return `/rolex/watches/${baseSlug}`;
}

/**
 * Create Rolex Feature List from specifications
 */
function createRolexFeatureList(row) {
  const specs = [
    { key: 'Spec_ModelCase', title: 'Model Case' },
    { key: 'Spec_Material', title: 'Material' },
    { key: 'Spec_Bezel', title: 'Bezel' },
    { key: 'Spec_WindingCrown', title: 'Winding Crown' },
    { key: 'Spec_Crystal', title: 'Crystal' },
    { key: 'Spec_WaterResistance', title: 'Water Resistance' },
    { key: 'Spec_Movement', title: 'Movement' },
    { key: 'Spec_Calibre', title: 'Calibre' },
    { key: 'Spec_Bracelet', title: 'Bracelet' },
    { key: 'Spec_BraceletMaterial', title: 'Bracelet Material' },
    { key: 'Spec_Clasp', title: 'Clasp' },
    { key: 'Spec_Dial', title: 'Dial' },
    { key: 'Spec_Gem_setting', title: 'Gem Setting' },
    { key: 'Spec_PowerReserve', title: 'Power Reserve' },
    { key: 'Spec_Certification', title: 'Certification' },
  ];

  const features = specs
    .map(({ key, title }, index) => {
      const value = cleanText(row[key]);
      if (value) {
        return {
          _key: generateUniqueKey(`feature-${index}`), // Add unique key
          title,
          description: value,
        };
      }
      return null;
    })
    .filter(Boolean);

  if (features.length === 0) return null;

  return {
    _type: 'rolexFeatureList',
    _key: generateUniqueKey('featureList'), // Add unique key
    features,
    cta: {
      link: '/rolex/contact',
      text: 'Contact Us',
    },
  };
}

/**
 * Create Rolex Rich Text from features
 */
async function createRolexRichText(row, watchRef, dryRun = false) {
  const features = [
    {
      title: cleanText(row.Feature1_Title),
      text: cleanText(row.Feature1_Text),
      asset: row.Feature1_Asset,
    },
    {
      title: cleanText(row.Feature2_Title),
      text: cleanText(row.Feature2_Text),
      asset: row.Feature2_Asset,
    },
    {
      title: cleanText(row.Feature3_Title),
      text: cleanText(row.Feature3_Text),
      asset: row.Feature3_Asset,
    },
  ].filter((feature) => feature.title || feature.text);

  if (features.length === 0) return null;

  const content = [];

  for (const [index, feature] of features.entries()) {
    if (feature.title) {
      // Add heading
      content.push({
        _type: 'block',
        _key: generateUniqueKey(`heading-${index}`), // Use index for uniqueness
        style: 'h2',
        children: [
          {
            _type: 'span',
            _key: generateUniqueKey(`span-${index}`), // Add unique key for span
            text: feature.title,
          },
        ],
      });
    }

    if (feature.text) {
      // Add paragraph text
      content.push({
        _type: 'block',
        _key: generateUniqueKey(`text-${index}`), // Use index for uniqueness
        style: 'normal',
        children: [
          {
            _type: 'span',
            _key: generateUniqueKey(`span-${index}`), // Add unique key for span
            text: feature.text,
          },
        ],
      });
    }

    // Add image if asset exists
    if (feature.asset && !dryRun) {
      const featureImage = await findFeatureAssetImage(feature.asset, watchRef, dryRun);
      if (featureImage) {
        content.push({
          _type: 'inlineImage',
          _key: generateUniqueKey(`image-${index}`), // Use index for uniqueness
          ...featureImage,
          alt: `${feature.title || 'Feature'} image`,
          centerImage: true,
        });
      }
    } else if (feature.asset && dryRun) {
      console.log(`  🖼️  Would add feature image for: ${feature.asset}`);
    }
  }

  if (content.length === 0) return null;

  return {
    _type: 'rolexRichText',
    _key: generateUniqueKey('richText'), // Add unique key
    alternativeBackgroundColor: false,
    content,
  };
}

/**
 * Create watch document from Excel row
 */
async function createWatchDocument(row, priceData = null, dryRun = false) {
  const rmc = row.RMC;
  const pricing = priceData ? priceData.find((p) => p.rmc === rmc) : null;

  // Basic information
  const title = cleanText(row.ModelName || row.H1);
  const familyName = cleanText(row.FamilyName);
  const size = extractSize(row.Spec_Diameter);
  const material = cleanText(row.Spec_Material);
  const category = CATEGORY_MAPPING[familyName] || 'classic';

  // Description from multiple sources
  const descriptionParts = [row.Spec_ModelCase, row.Feature1_Text, row.Feature2_Text, row.Feature3_Text].filter(
    Boolean
  );

  const description =
    descriptionParts.map(cleanText).join(' ').substring(0, 500) +
    (descriptionParts.join(' ').length > 500 ? '...' : '');

  // Release date (default to current date if not specified)
  const releaseDate = new Date().toISOString().split('T')[0];

  // Upload images (use RMC which has the full model code)
  const images = await findAndUploadWatchImages(rmc, dryRun);

  // Create content blocks
  const featureList = createRolexFeatureList(row);
  const richText = await createRolexRichText(row, rmc, dryRun);

  // Build content array
  const contentBlocks = [];

  if (featureList) {
    contentBlocks.push(featureList);
  }

  if (richText) {
    contentBlocks.push(richText);
  }

  const document = {
    _type: 'rolexWatch',
    title,
    slug: {
      _type: 'slug',
      current: generateSlug(title, rmc, releaseDate),
    },
    releaseDate,
    description,
    type: familyName,
    size,
    material,
    category,

    // Main watch image (for collection page)
    image: images.heroImage,

    // Header images
    rolexHeaderImage: images.heroImage,
    rolexHeaderImageMobile: images.heroImage,

    // Content blocks
    content: contentBlocks,

    // Additional technical specifications (keeping for backwards compatibility)
    rmc,
    reference: cleanText(row.Reference),
    dialColor: cleanText(row.Spec_Dial),
    movement: cleanText(row.Spec_Movement),
    calibre: cleanText(row.Spec_Calibre),
    waterResistance: cleanText(row.Spec_WaterResistance),
    powerReserve: cleanText(row.Spec_PowerReserve),
    bezel: cleanText(row.Spec_Bezel),
    bracelet: cleanText(row.Spec_Bracelet),

    // Features array (keeping for backwards compatibility)
    features: [
      {
        _key: generateUniqueKey('feature-1'), // Add unique key
        title: cleanText(row.Feature1_Title),
        text: cleanText(row.Feature1_Text),
        asset: row.Feature1_Asset,
      },
      {
        _key: generateUniqueKey('feature-2'), // Add unique key
        title: cleanText(row.Feature2_Title),
        text: cleanText(row.Feature2_Text),
        asset: row.Feature2_Asset,
      },
      {
        _key: generateUniqueKey('feature-3'), // Add unique key
        title: cleanText(row.Feature3_Title),
        text: cleanText(row.Feature3_Text),
        asset: row.Feature3_Asset,
      },
    ].filter((feature) => feature.title || feature.text),

    // Hero image
    heroImage: images.heroImage,

    // Gallery images (uploaded from local files)
    galleryImages: images.galleryImages,

    // Feature images
    featureImages: images.featureImages,

    // Brochure URL
    brochureUrl: row.Brochure,

    // Pricing (if available)
    ...(pricing && {
      retailPrice: pricing['Rolex maximum suggested retail prices'],
      currency: pricing.currency || 'GBP',
    }),
  };

  return document;
}

/**
 * Clear watches with exact same titles
 */
async function clearExistingWatches(documents, dryRun = false) {
  const titles = documents.map((doc) => doc.title);
  const uniqueTitles = [...new Set(titles)];

  console.log(`\n${dryRun ? 'DRY RUN - ' : ''}Checking for existing watches with matching titles...\n`);

  for (const title of uniqueTitles) {
    try {
      const existing = await client.fetch(`*[_type == "rolexWatch" && title == $title]`, { title });

      if (existing.length > 0) {
        if (dryRun) {
          console.log(`Would delete ${existing.length} existing watch(es) with title: "${title}"`);
        } else {
          console.log(`🗑️  Deleting ${existing.length} existing watch(es) with title: "${title}"`);
          for (const watch of existing) {
            await client.delete(watch._id);
          }
        }
      }
    } catch (error) {
      console.error(`Error clearing watches with title "${title}":`, error.message);
    }
  }

  if (!dryRun && uniqueTitles.length > 0) {
    console.log('✅ Clearing completed!');
  }
}

/**
 * Upload documents to Sanity
 */
async function uploadToSanity(documents, dryRun = false, clearFlag = false) {
  console.log(`\n${dryRun ? 'DRY RUN - ' : ''}Processing ${documents.length} watch documents...\n`);

  // Clear existing watches with same titles if flag is set
  if (clearFlag) {
    await clearExistingWatches(documents, dryRun);
  }

  for (let i = 0; i < documents.length; i++) {
    const doc = documents[i];

    try {
      if (dryRun) {
        console.log(`[${i + 1}/${documents.length}] Would create: ${doc.title} (${doc.rmc})`);
        console.log(`  - Category: ${doc.category}`);
        console.log(`  - Size: ${doc.size}mm`);
        console.log(`  - Material: ${doc.material}`);
        console.log(`  - Slug: ${doc.slug.current}`);
        console.log(`  - Content blocks: ${doc.content.length}`);
        if (doc.retailPrice) {
          console.log(`  - Price: £${doc.retailPrice.toLocaleString()}`);
        }
        console.log();
      } else {
        // Check if document already exists (by RMC, not title)
        const existing = await client.fetch(`*[_type == "rolexWatch" && rmc == $rmc][0]`, { rmc: doc.rmc });

        if (existing) {
          console.log(`[${i + 1}/${documents.length}] Updating: ${doc.title} (${doc.rmc})`);
          await client.patch(existing._id).set(doc).commit();
        } else {
          console.log(`[${i + 1}/${documents.length}] Creating: ${doc.title} (${doc.rmc})`);
          await client.create(doc);
        }
      }
    } catch (error) {
      console.error(`Error processing ${doc.title}:`, error.message);
    }
  }

  if (!dryRun) {
    console.log('\n✅ Upload completed!');
  } else {
    console.log('\n📋 Dry run completed. Use --execute to actually upload the data.');
  }
}

/**
 * Main execution function
 */
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  const clearFlag = args.includes('--clear');
  const familyFilter = args.find((arg) => arg.startsWith('--family='))?.split('=')[1];

  console.log('🏗️  Rolex Watch Population Script');
  console.log('================================');

  // Validate files exist
  if (!fs.existsSync(SPECS_FILE)) {
    console.error(`❌ Specifications file not found: ${SPECS_FILE}`);
    process.exit(1);
  }

  if (!fs.existsSync(PRICES_FILE)) {
    console.warn(`⚠️  Pricing file not found: ${PRICES_FILE}`);
  }

  // Read data
  console.log('📊 Reading Excel files...');
  const specsData = readExcelFile(SPECS_FILE, 'Watches List');
  const pricesData = fs.existsSync(PRICES_FILE) ? readExcelFile(PRICES_FILE, 'Suggested Retail PricesGB') : [];

  console.log(`Found ${specsData.length} watch specifications`);
  console.log(`Found ${pricesData.length} pricing records`);

  // Filter by family if specified
  let filteredData = specsData;
  if (familyFilter) {
    filteredData = specsData.filter((row) => row.FamilyName?.toLowerCase().includes(familyFilter.toLowerCase()));
    console.log(`Filtered to ${filteredData.length} watches for family: ${familyFilter}`);
  }

  // Create documents
  console.log('\n🔄 Processing watch data...');
  const filteredRows = filteredData.filter((row) => row.RMC && row.ModelName); // Only process rows with essential data

  const documents = [];
  for (let i = 0; i < filteredRows.length; i++) {
    const row = filteredRows[i];
    console.log(`\n[${i + 1}/${filteredRows.length}] Processing: ${row.ModelName} (${row.RMC})`);
    const document = await createWatchDocument(row, pricesData, dryRun);
    documents.push(document);
  }

  // Group by category for summary
  const categoryStats = documents.reduce((acc, doc) => {
    acc[doc.category] = (acc[doc.category] || 0) + 1;
    return acc;
  }, {});

  console.log('\n📈 Summary:');
  Object.entries(categoryStats).forEach(([category, count]) => {
    console.log(`  ${category}: ${count} watches`);
  });

  // Upload to Sanity
  await uploadToSanity(documents, dryRun, clearFlag);

  if (dryRun) {
    console.log('\n💡 Usage options:');
    console.log('  --execute              Actually upload to Sanity');
    console.log('  --clear                Remove existing watches with exact same titles');
    console.log('  --family=Submariner    Filter by specific family');
    console.log('\nExample: node scripts/populate-rolex-watches.js --family=Submariner --clear --execute');
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { createWatchDocument, generateSlug };
