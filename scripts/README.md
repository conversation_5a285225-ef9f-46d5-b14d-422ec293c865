# Rolex Watch Population Script

This script automates the population of Rolex watches in Sanity CMS from the official Rolex Excel data files.

## Prerequisites

1. **Excel Files**: Download the official Rolex selection files to `~/Downloads/Official Rolex Selection/`:

   - `technical_specifications_rolex_selection_en.xlsx` (main data source)
   - `RolexMaximumSuggestedRetailPrices_GB.xlsx` (pricing data)
   - `2025_official_rolex_selection_alignment.xlsx` (optional)

2. **Environment Variables**: Ensure these are set in your `.env.local`:

   ```bash
   NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
   NEXT_PUBLIC_SANITY_DATASET=your_dataset
   SANITY_API_TOKEN=your_write_token
   ```

3. **Dependencies**: The script requires `xlsx` package (automatically installed).

## Usage

### Basic Commands

```bash
# Dry run (preview what will be created)
npm run populate-rolex

# Actually upload to Sanity
npm run populate-rolex -- --execute

# Filter by specific watch family
npm run populate-rolex -- --family=Submariner

# Upload specific family
npm run populate-rolex -- --family=Submariner --execute
```

### Advanced Usage

```bash
# Direct node execution
node scripts/populate-rolex-watches.js --execute

# Filter by multiple families (run separately)
npm run populate-rolex -- --family=Datejust --execute
npm run populate-rolex -- --family=Cosmograph --execute
```

## Data Mapping

The script maps Excel data to the Sanity schema as follows:

### Core Fields

- `title` ← `ModelName` from Excel
- `type` ← `FamilyName`
- `size` ← Extracted from `Spec_Diameter`
- `material` ← `Spec_Material`
- `category` ← Auto-mapped based on family (classic/professional/perpetual)
- `description` ← Combined from specifications and features

### Additional Fields Created

- `rmc` - Rolex Model Code (unique identifier)
- `reference` - Reference number
- `dialColor`, `movement`, `calibre` - Technical specifications
- `waterResistance`, `powerReserve` - Performance specs
- `features[]` - Array of key features with titles and descriptions
- `galleryImages[]` - Array of product image URLs
- `retailPrice` - UK pricing (if available)

### Category Mapping

- **Professional**: Submariner, GMT-Master, Daytona, Explorer, etc.
- **Classic**: Datejust, Oyster Perpetual, Lady-Datejust
- **Perpetual**: Day-Date, Sky-Dweller, Cellini

## Script Features

### Safety Features

- **Dry Run by Default**: Preview changes before executing
- **Duplicate Detection**: Updates existing watches instead of creating duplicates
- **Data Validation**: Skips rows missing essential data (RMC, ModelName)
- **Error Handling**: Continues processing if individual watches fail

### Data Processing

- **Text Cleaning**: Removes extra whitespace and formatting
- **Slug Generation**: Auto-generates SEO-friendly URLs
- **Size Extraction**: Parses diameter specifications to numeric values
- **Feature Arrays**: Structures multiple features into organized arrays
- **Pricing Integration**: Merges pricing data with specifications

### Output

- Progress tracking with item counts
- Category summaries
- Error reporting for failed items
- Success confirmation

## Schema Extensions

Consider extending the `rolexWatchType.ts` schema to capture additional fields:

```typescript
// Add these fields to capture richer data
defineField({
  name: 'rmc',
  type: 'string',
  title: 'RMC Code',
  description: 'Rolex Model Code - unique identifier',
}),
  defineField({
    name: 'dialColor',
    type: 'string',
    title: 'Dial Color',
  }),
  defineField({
    name: 'movement',
    type: 'string',
    title: 'Movement',
  }),
  defineField({
    name: 'retailPrice',
    type: 'number',
    title: 'Retail Price (GBP)',
  }),
  defineField({
    name: 'features',
    type: 'array',
    title: 'Key Features',
    of: [
      {
        type: 'object',
        fields: [
          { name: 'title', type: 'string' },
          { name: 'text', type: 'text' },
          { name: 'asset', type: 'string' },
        ],
      },
    ],
  });
```

## Troubleshooting

### Common Issues

1. **File Not Found**: Ensure Excel files are in the correct directory
2. **Permission Errors**: Check SANITY_API_TOKEN has write permissions
3. **Network Issues**: Verify internet connection for Sanity uploads
4. **Data Errors**: Review console output for specific validation failures

### Debug Mode

Add console.log statements in the script for detailed debugging:

```javascript
// In createWatchDocument function
console.log('Processing:', row.ModelName, 'RMC:', row.RMC);
```

### Recovery

If the script fails partway through:

- Use `--family=` flag to process specific collections
- Check Sanity Studio to see what was successfully created
- Re-run with `--execute` - the script handles duplicates safely

## Output Example

```
🏗️  Rolex Watch Population Script
================================
📊 Reading Excel files...
Found 100 watch specifications
Found 100 pricing records

🔄 Processing watch data...

📈 Summary:
  professional: 45 watches
  classic: 35 watches
  perpetual: 20 watches

[1/100] Creating: Submariner Date (M126610LN-0001)
  - Category: professional
  - Size: 41mm
  - Material: Oystersteel
  - Slug: /rolex/watches/submariner-date
  - Price: £8,550

✅ Upload completed!
```

## Performance

- Processes ~100 watches in 2-3 minutes
- Uses batch operations where possible
- Respects Sanity API rate limits
- Memory efficient for large datasets

## Maintenance

Update the script when:

- New Rolex families are introduced
- Schema fields change
- Excel file structure changes
- Category mappings need adjustment
