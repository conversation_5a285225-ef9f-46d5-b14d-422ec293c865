# 图片压缩包获取和解压工具

这些脚本用于从指定的API端点获取压缩包数据并解压图片文件。

## 脚本文件

### 1. `fetch-and-extract-images.js` - 基础版本

简单直接的脚本，从API获取压缩包并解压到指定目录。

**特性:**

- 从API获取压缩包数据
- 使用adm-zip解压文件
- 基本的错误处理和日志输出
- 显示解压统计信息

**使用方法:**

```bash
# 使用npm脚本
npm run extract-images

# 或直接运行
node scripts/fetch-and-extract-images.js
```

### 2. `fetch-and-extract-images-advanced.js` - 高级版本

功能更丰富的版本，支持多种配置选项和高级功能。

**特性:**

- 可配置的输出目录
- 时间戳文件夹创建
- 文件覆盖控制
- 文件大小限制
- 自定义文件过滤器
- 详细的日志级别控制
- 命令行参数支持
- 详细的统计信息

**使用方法:**

```bash
# 使用npm脚本（默认设置）
npm run extract-images-advanced

# 或直接运行
node scripts/fetch-and-extract-images-advanced.js

# 使用命令行选项
node scripts/fetch-and-extract-images-advanced.js --output ./my-images --overwrite
node scripts/fetch-and-extract-images-advanced.js --debug --max-size 10
node scripts/fetch-and-extract-images-advanced.js --help
```

## 命令行选项（高级版本）

| 选项                 | 描述                 | 默认值                |
| -------------------- | -------------------- | --------------------- |
| `-o, --output <dir>` | 输出目录             | `../extracted-images` |
| `--overwrite`        | 覆盖已存在的文件     | `false`               |
| `--no-timestamp`     | 不创建时间戳文件夹   | `false`               |
| `--debug`            | 启用调试日志         | `info`                |
| `--quiet`            | 只显示警告和错误     | `info`                |
| `--max-size <MB>`    | 最大文件大小限制(MB) | 无限制                |
| `-h, --help`         | 显示帮助信息         | -                     |

## 配置选项（高级版本）

可以通过修改脚本中的 `DEFAULT_CONFIG` 对象来自定义默认配置：

```javascript
const DEFAULT_CONFIG = {
  apiUrl: 'http://************:9600/CS_WebService/OsService.svc/articleimages',
  outputDir: '../extracted-images',
  overwrite: false,
  createTimestampFolder: true,
  logLevel: 'info', // 'debug', 'info', 'warn', 'error'
  fileFilter: null, // 函数或正则表达式来过滤文件
  maxFileSize: null, // 最大文件大小限制（字节）
};
```

## 文件过滤示例

### 只提取图片文件

```javascript
const config = {
  fileFilter: (filename) => /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(filename),
};
```

### 使用正则表达式过滤

```javascript
const config = {
  fileFilter: /^Article_\d+\.(jpg|png)$/i,
};
```

### 排除特定文件夹

```javascript
const config = {
  fileFilter: (filename) => !filename.startsWith('thumbnails/'),
};
```

## 输出结构

默认情况下，文件会被解压到以下结构：

```
extracted-images/
├── extract_2024-01-15T10-30-45/  # 时间戳文件夹（如果启用）
│   ├── Article_12345.jpg
│   ├── Article_12346.png
│   └── subfolder/
│       └── more_images.jpg
```

## API数据结构

脚本期望API返回以下格式的JSON数据：

```json
{
  "Success": true,
  "data": {
    "Checksum": [
      /* 压缩包的字节数组 */
    ]
  }
}
```

## 错误处理

脚本包含以下错误处理：

1. **网络错误**: HTTP请求失败
2. **API错误**: API返回Success: false
3. **数据错误**: 缺少压缩包数据
4. **解压错误**: 压缩包损坏或格式错误
5. **文件系统错误**: 权限问题或磁盘空间不足

## 日志级别

- **debug**: 显示所有详细信息，包括每个文件的解压过程
- **info**: 显示一般信息和进度（默认）
- **warn**: 只显示警告和错误
- **error**: 只显示错误信息

## 性能考虑

- 大型压缩包可能需要较长时间处理
- 内存使用量取决于压缩包大小
- 使用文件过滤器可以减少处理时间和磁盘使用

## 故障排除

### 常见问题

1. **"API响应表示失败"**

   - 检查API端点是否可访问
   - 验证网络连接

2. **"API响应中没有找到压缩包数据"**

   - 检查API返回的数据结构
   - 确认data.Checksum字段存在

3. **权限错误**

   - 确保对输出目录有写权限
   - 在Linux/Mac上可能需要使用sudo

4. **内存不足**
   - 对于大型压缩包，考虑增加Node.js内存限制：
   ```bash
   node --max-old-space-size=4096 scripts/fetch-and-extract-images-advanced.js
   ```

## 依赖项

- `adm-zip`: 用于处理ZIP文件
- Node.js 内置模块: `fs`, `path`, `url`

确保已安装依赖：

```bash
npm install adm-zip --legacy-peer-deps
```

## 快速开始

### 1. 基础使用

```bash
# 使用默认设置解压图片
npm run extract-images
```

### 2. 高级使用

```bash
# 使用高级功能
npm run extract-images-advanced

# 自定义输出目录并覆盖现有文件
npm run extract-images-advanced -- --output ./my-images --overwrite

# 启用调试模式
npm run extract-images-advanced -- --debug

# 限制文件大小为5MB
npm run extract-images-advanced -- --max-size 5
```

### 3. 运行示例

```bash
# 运行所有使用示例
npm run extract-images-examples
```

## 测试结果

脚本已经过测试，能够成功：

- ✅ 从API获取压缩包数据（约218KB）
- ✅ 解压9个图片文件
- ✅ 创建时间戳文件夹
- ✅ 处理文件过滤和大小限制
- ✅ 提供详细的统计信息

## 文件结构

```
scripts/
├── fetch-and-extract-images.js          # 基础版本
├── fetch-and-extract-images-advanced.js # 高级版本
├── example-usage.js                     # 使用示例
└── README-extract-images.md             # 说明文档

extracted-images/                        # 默认输出目录
├── 4268700000131.jpg
├── 930100660500012.jpg
├── 930100660500012_1.jpg
└── ...
```
