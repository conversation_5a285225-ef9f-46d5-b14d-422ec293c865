#!/usr/bin/env node

/**
 * Manual Product Sync Script
 *
 * This script manually syncs product data from the external API to Sanity database.
 * It replicates the logic from src/app/api/sync-products/route.ts but runs as a standalone script.
 *
 * Usage: node scripts/manual-sync-products.js [--dry-run]
 *
 * Options:
 *   --dry-run    Show what would be synced without actually uploading to Sanity
 */

import { createClient } from '@sanity/client';
import AdmZip from 'adm-zip';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

// Load environment variables from .env.local if available
try {
  dotenv.config({ path: '.env.local' });
} catch (e) {
  // dotenv not available, continue with process.env
}

// Validate required environment variables
const requiredEnvVars = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN,
};

function validateEnvironment() {
  console.log('🔍 Checking environment variables...');
  console.log('Project ID:', requiredEnvVars.projectId ? '✅ Set' : '❌ Missing');
  console.log('Dataset:', requiredEnvVars.dataset ? '✅ Set' : '❌ Missing');
  console.log('Token:', requiredEnvVars.token ? '✅ Set' : '❌ Missing');

  const missing = Object.entries(requiredEnvVars)
    .filter(([, value]) => !value)
    .map(([key]) => key);

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach((key) => {
      const envVar =
        key === 'projectId'
          ? 'NEXT_PUBLIC_SANITY_PROJECT_ID'
          : key === 'dataset'
            ? 'NEXT_PUBLIC_SANITY_DATASET'
            : 'SANITY_API_TOKEN';
      console.error(`  - ${envVar}`);
    });
    console.error('\nPlease set these in your .env.local file or environment.');
    process.exit(1);
  }
}

// Sanity client configuration
let client;
try {
  validateEnvironment();
  console.log('🔧 Initializing Sanity client...');
  client = createClient({
    projectId: requiredEnvVars.projectId,
    dataset: requiredEnvVars.dataset,
    useCdn: false,
    token: requiredEnvVars.token,
    apiVersion: '2024-01-01',
  });
  console.log('✅ Sanity client initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Sanity client:', error.message);
  process.exit(1);
}

// API endpoints
const API_ENDPOINTS = {
  articles: 'http://62.7.112.233:9600/CS_WebService/OsService.svc/articles',
  images: 'http://62.7.112.233:9600/CS_WebService/OsService.svc/articleimages',
};

/**
 * Function to upload image to Sanity
 */
async function uploadImageToSanity(imageBuffer, fileName) {
  try {
    const asset = await client.assets.upload('image', imageBuffer, {
      filename: fileName,
    });
    return {
      _type: 'image',
      asset: {
        _ref: asset._id,
        _type: 'reference',
      },
    };
  } catch (error) {
    console.error(`Error uploading image ${fileName}:`, error);
    return null;
  }
}

/**
 * Fetch data from external API
 */
async function fetchApiData() {
  console.log('📡 Fetching data from external APIs...');

  try {
    const [imageResponse, articleResponse] = await Promise.all([
      fetch(API_ENDPOINTS.images),
      fetch(API_ENDPOINTS.articles),
    ]);

    if (!imageResponse.ok || !articleResponse.ok) {
      throw new Error(`HTTP error! Image: ${imageResponse.status}, Article: ${articleResponse.status}`);
    }

    const articleData = await articleResponse.json();
    const imageData = await imageResponse.json();

    console.log('📋 API Response Status:');
    console.log(`  - Article API Success: ${articleData.Success}`);
    console.log(`  - Image API Success: ${imageData.Success}`);

    if (articleData.data) {
      console.log(`  - Articles count: ${articleData.data.length}`);
    } else {
      console.log(`  - Article data: ${JSON.stringify(articleData, null, 2)}`);
    }

    if (imageData.data) {
      console.log(`  - Image data available: ${imageData.data.Checksum ? 'Yes' : 'No'}`);
    } else {
      console.log(`  - Image data: ${JSON.stringify(imageData, null, 2)}`);
    }

    if (!imageData.Success || !articleData.Success) {
      console.error('❌ API Response Details:');
      if (!articleData.Success) {
        console.error('  - Article API failed:', JSON.stringify(articleData, null, 2));
      }
      if (!imageData.Success) {
        console.error('  - Image API failed:', JSON.stringify(imageData, null, 2));
      }
      throw new Error('API response indicates failure');
    }

    console.log(`✅ Successfully fetched ${articleData.data.length} articles`);
    console.log(`✅ Successfully fetched image data (${imageData.data.Checksum ? 'ZIP available' : 'No ZIP data'})`);

    return { articleData: articleData.data, imageData: imageData.data };
  } catch (error) {
    console.error('❌ Error fetching API data:', error);
    throw error;
  }
}

/**
 * Process images from ZIP data
 */
async function processImages(imageData, dryRun = false) {
  console.log('🖼️  Processing images from ZIP data...');

  if (!imageData.Checksum) {
    console.warn('⚠️  No image ZIP data available');
    return new Map();
  }

  try {
    // Process image data from zip
    const bytes = imageData.Checksum;
    const buffer = Buffer.from(bytes);

    // Create AdmZip instance to extract images from buffer
    const zip = new AdmZip(buffer);
    const zipEntries = zip.getEntries();

    console.log(`📦 Found ${zipEntries.length} entries in ZIP file`);

    // Create a map to store images by EAN (filename without extension)
    const imageMap = new Map();

    let processedCount = 0;
    let uploadedCount = 0;

    // Process each image in the zip
    for (const entry of zipEntries) {
      if (!entry.isDirectory && entry.entryName.match(/\.(jpg|jpeg|png|webp)$/i)) {
        try {
          // Extract filename without extension to match with EAN
          const fileName = entry.entryName.split('/').pop() || '';
          const eanFromFileName = fileName.replace(/\.(jpg|jpeg|png|webp)$/i, '');

          processedCount++;

          if (dryRun) {
            console.log(`  [DRY RUN] Would process image: ${fileName} -> EAN: ${eanFromFileName}`);
            imageMap.set(eanFromFileName, { _type: 'image', asset: { _ref: 'mock-ref', _type: 'reference' } });
          } else {
            // Get image buffer
            const imageBuffer = entry.getData();

            // Upload to Sanity
            const uploadedImage = await uploadImageToSanity(imageBuffer, fileName);

            if (uploadedImage) {
              imageMap.set(eanFromFileName, uploadedImage);
              uploadedCount++;
              console.log(`  ✅ Uploaded image for EAN: ${eanFromFileName}`);
            }
          }
        } catch (error) {
          console.error(`  ❌ Error processing image ${entry.entryName}:`, error);
        }
      }
    }

    console.log(`📊 Image processing summary:`);
    console.log(`  - Total image files found: ${processedCount}`);
    console.log(`  - Successfully ${dryRun ? 'would upload' : 'uploaded'}: ${dryRun ? processedCount : uploadedCount}`);

    return imageMap;
  } catch (error) {
    console.error('❌ Error processing images:', error);
    return new Map();
  }
}

/**
 * Create product document from article data
 */
function createProductDocument(article, imageMap) {
  const {
    Ean,
    Title,
    Description,
    Brand,
    Collection,
    Price,
    ArticleGroup,
    ArticleGroupId,
    ReferenceNo,
    Stock,
    ProductGroup,
    ProductLine,
    ArtikelSpecialData,
    Designations,
    specialData,
    specialAttributes,
    Guarantee,
  } = article;

  // Determine product type based on ArticleGroup
  let productType = '';
  let baseUrl = '';
  if (ArticleGroup === 'Watches' || ArticleGroup === 'Pre-Owned-Watches') {
    productType = 'watchProduct';
    baseUrl = 'watches';
  } else if (ArticleGroup === 'Diamond-Jewelry' || ArticleGroup === '18ct-Jewelry') {
    productType = 'jewelryProduct';
    baseUrl = 'jewelry';
  } else {
    productType = 'watchProduct'; // Default fallback
    baseUrl = 'watches';
  }

  // Prepare product document
  const productId = `${productType}-${Ean}`;
  const product = {
    _id: productId,
    _type: productType,
    ean: Ean,
    title: Title,
    description: Designations.find((d) => d.LngIso === 'en')?.Beschreibung || Description,
    brand: Brand,
    collection: Collection,
    price: Price,
    articleGroup: {
      name: ArticleGroup,
      id: ArticleGroupId,
    },
    referenceNo: ReferenceNo,
    stock: Stock,
    productGroup: ProductGroup,
    productLine: ProductLine,
    specialData: specialData.map(({ AttributeName, AttributeValue }) => ({
      _key: uuidv4(),
      name: AttributeName,
      value: AttributeValue,
    })),
    specialAttributes: specialAttributes.map(({ AttributeName, AttributeValue }) => ({
      _key: uuidv4(),
      name: AttributeName,
      value: AttributeValue,
    })),
    slug: {
      _type: 'slug',
      current: `${baseUrl}/${Brand.toLowerCase()}/${Ean.toLowerCase()}`,
    },
    // Initialize optional properties as null
    watchDetails: null,
    jewelryDetails: null,
    image: null,
  };

  // Add watch-specific data if applicable
  if (ArticleGroup === 'Watches' || ArticleGroup === 'Pre-Owned-Watches') {
    const { ArtikelUhr } = ArtikelSpecialData;
    product.watchDetails = {
      strapType: ArtikelUhr.strapType,
      strapColour: ArtikelUhr.Armband,
      caseWidth: ArtikelUhr.Gehaeusebreite,
      caseShape: ArtikelUhr.Gehaeuseform,
      caseDepth: ArtikelUhr.Gehaeusehoehe,
      caseMaterial: ArtikelUhr.Gehaeusematerial,
      glassType: ArtikelUhr.Glastyp,
      dial: ArtikelUhr.Zifferblatt,
      dialColor: ArtikelUhr.ZifferblattFarbe,
      movement: ArtikelUhr.Uhrwerk,
      claspType: ArtikelUhr.Schliesse,
      waterproofRating: ArtikelUhr.Wasserdicht,
      warranty: Guarantee,
      packaging: '',
      sku: ArtikelUhr.LiefArtNr,
      IsDatum: ArtikelUhr.IsDatum,
      IsGangreserve: ArtikelUhr.IsGangreserve,
      IsMondphase: ArtikelUhr.IsMondphase,
      IsSekunde: ArtikelUhr.IsSekunde,
      IsTagDatum: ArtikelUhr.IsTagDatum,
      IsTaucher: ArtikelUhr.IsTaucher,
      IsZweiteZeitZone: ArtikelUhr.IsZweiteZeitZone,
    };
  }

  // Add jewelry-specific data if applicable
  if (ArticleGroup === 'Diamond-Jewelry' || ArticleGroup === '18ct-Jewelry') {
    const { ArtikelSchmuck, DiamantenListe } = ArtikelSpecialData;
    product.jewelryDetails = {
      sizeType: ArtikelSchmuck.GroessenTyp,
      serviceInterval: ArtikelSchmuck.ServiceIntervall,
      diamonds: DiamantenListe.map((diamond) => ({
        _key: uuidv4(),
        carat: diamond.GesamtCarat,
        clarity: diamond.Clarity,
        color: diamond.Color,
        cut: diamond.Cut,
        cutQuality: diamond.CutQualitaet,
      })),
    };
  }

  // Attach main image if available
  const mainImage = imageMap.get(Ean);
  if (mainImage) {
    product.image = mainImage;
  }

  return product;
}

/**
 * Sync products to Sanity
 */
async function syncProducts(articles, imageMap, dryRun = false) {
  console.log(`🔄 ${dryRun ? '[DRY RUN] ' : ''}Processing ${articles.length} articles...`);

  const stats = {
    total: articles.length,
    watches: 0,
    jewelry: 0,
    withImages: 0,
    processed: 0,
    errors: 0,
  };

  if (dryRun) {
    // Dry run - just analyze the data
    for (const article of articles) {
      try {
        const product = createProductDocument(article, imageMap);

        if (product._type === 'watchProduct') stats.watches++;
        if (product._type === 'jewelryProduct') stats.jewelry++;
        if (product.image) stats.withImages++;

        stats.processed++;

        console.log(`  [${stats.processed}/${stats.total}] ${product._type}: ${product.title} (${product.ean})`);
        console.log(`    - Brand: ${product.brand}, Collection: ${product.collection}`);
        console.log(`    - Price: ${product.price}, Stock: ${product.stock}`);
        console.log(`    - Image: ${product.image ? '✅ Available' : '❌ Missing'}`);
        console.log(`    - Slug: ${product.slug.current}`);
        console.log();
      } catch (error) {
        stats.errors++;
        console.error(`  ❌ Error processing article ${article.Ean}:`, error.message);
      }
    }
  } else {
    // Actual sync to Sanity
    const transaction = client.transaction();

    for (const article of articles) {
      try {
        const product = createProductDocument(article, imageMap);

        if (product._type === 'watchProduct') stats.watches++;
        if (product._type === 'jewelryProduct') stats.jewelry++;
        if (product.image) stats.withImages++;

        // Add to transaction (create or update)
        transaction.createIfNotExists(product).patch(product._id, (p) => p.set(product));

        stats.processed++;
        console.log(`  [${stats.processed}/${stats.total}] ✅ Queued: ${product.title} (${product.ean})`);
      } catch (error) {
        stats.errors++;
        console.error(`  ❌ Error processing article ${article.Ean}:`, error.message);
      }
    }

    // Commit transaction
    console.log('\n💾 Committing transaction to Sanity...');
    await transaction.commit();
    console.log('✅ Transaction committed successfully!');
  }

  // Print summary
  console.log('\n📊 Sync Summary:');
  console.log(`  - Total articles: ${stats.total}`);
  console.log(`  - Watch products: ${stats.watches}`);
  console.log(`  - Jewelry products: ${stats.jewelry}`);
  console.log(`  - Products with images: ${stats.withImages}`);
  console.log(`  - Successfully processed: ${stats.processed}`);
  console.log(`  - Errors: ${stats.errors}`);

  return stats;
}

/**
 * Main execution function
 */
async function main() {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');

  console.log('🚀 Manual Product Sync Script');
  console.log('==============================');
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE SYNC'}`);
  console.log();

  try {
    // Step 1: Fetch data from APIs
    const { articleData, imageData } = await fetchApiData();

    // Step 2: Process images
    const imageMap = await processImages(imageData, dryRun);

    // Step 3: Sync products
    await syncProducts(articleData, imageMap, dryRun);

    console.log('\n🎉 Sync completed successfully!');

    if (dryRun) {
      console.log('\n💡 To perform the actual sync, run:');
      console.log('   node scripts/manual-sync-products.js');
    }
  } catch (error) {
    console.error('\n❌ Sync failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);
