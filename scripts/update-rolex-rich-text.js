#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update Rolex Rich Text Calibre content from Excel data
 *
 * Usage: node scripts/update-rolex-rich-text.js [--execute] [--rmc=M123456]
 *
 * This script:
 * 1. Reads technical specifications Excel file
 * 2. Finds watches in Sanity by RMC
 * 3. Updates the Rolex Rich Text Calibre component content
 */

import XLSX from 'xlsx';
import { createClient } from '@sanity/client';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Validate environment variables
const requiredEnvVars = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN,
};

function validateEnvironment() {
  const missing = Object.entries(requiredEnvVars)
    .filter(([, value]) => !value)
    .map(([key]) => key);

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach((key) => {
      const envVar =
        key === 'projectId'
          ? 'NEXT_PUBLIC_SANITY_PROJECT_ID'
          : key === 'dataset'
            ? 'NEXT_PUBLIC_SANITY_DATASET'
            : 'SANITY_API_TOKEN';
      console.error(`  - ${envVar}`);
    });
    process.exit(1);
  }
}

validateEnvironment();

// Initialize Sanity client
const client = createClient({
  projectId: requiredEnvVars.projectId,
  dataset: requiredEnvVars.dataset,
  token: requiredEnvVars.token,
  useCdn: false,
  apiVersion: '2024-01-01',
});

// File paths
const DATA_DIR = './scripts/Official Rolex Selection';
const SPECS_FILE = path.join(DATA_DIR, 'technical_specifications_rolex_selection_en.xlsx');
const IMAGES_DIR = path.join(DATA_DIR, 'retailer-selection_webp');

/**
 * Generate a unique key for Sanity blocks
 */
function generateKey() {
  return Math.random().toString(36).substr(2, 9);
}

/**
 * Clean text content
 */
function cleanText(text) {
  if (!text) return '';
  return text.toString().trim().replace(/\s+/g, ' ');
}

/**
 * Find and upload image from local filesystem
 */
async function findAndUploadImage(assetPath, rmc) {
  if (!assetPath) return null;

  try {
    console.log(`   🔍 Looking for image: ${assetPath}`);

    // Try different file extensions
    const extensions = ['.webp', '.jpg', '.jpeg', '.png'];

    for (const ext of extensions) {
      const fullPath = path.join(IMAGES_DIR, assetPath + ext);
      console.log(`     📁 Checking: ${fullPath}`);

      if (fs.existsSync(fullPath)) {
        console.log(`   ✅ Found image: ${fullPath}`);

        // Upload to Sanity
        const imageBuffer = fs.readFileSync(fullPath);
        const filename = path.basename(fullPath);

        console.log(`   📤 Uploading image to Sanity: ${filename}`);

        const asset = await client.assets.upload('image', imageBuffer, {
          filename,
          title: `Rolex ${rmc} Feature Image`,
          description: `Feature image for ${rmc}`,
        });

        console.log(`   ✅ Image uploaded successfully: ${asset._id}`);

        return {
          _type: 'reference',
          _ref: asset._id,
        };
      }
    }

    console.log(`   ⚠️  Image not found: ${assetPath}`);
    return null;
  } catch (error) {
    console.error(`   ❌ Error processing image ${assetPath}:`, error.message);
    return null;
  }
}

/**
 * Create rich text content from Excel data
 */
async function createRichTextContent(excelRow, rmc) {
  const content = [];

  // Process Feature 1, 2, 3
  for (let i = 1; i <= 3; i++) {
    const titleField = `Feature${i}_Title`;
    const textField = `Feature${i}_Text`;
    const assetField = `Feature${i}_Asset`;

    const title = excelRow[titleField];
    const text = excelRow[textField];
    const assetPath = excelRow[assetField];

    // Add title (Heading 1)
    if (title && cleanText(title)) {
      content.push({
        _key: generateKey(),
        _type: 'block',
        children: [
          {
            _key: generateKey(),
            _type: 'span',
            marks: [],
            text: cleanText(title),
          },
        ],
        markDefs: [],
        style: 'h1',
      });
    }

    // Add text (Normal)
    if (text && cleanText(text)) {
      content.push({
        _key: generateKey(),
        _type: 'block',
        children: [
          {
            _key: generateKey(),
            _type: 'span',
            marks: [],
            text: cleanText(text),
          },
        ],
        markDefs: [],
        style: 'normal',
      });
    }

    // Add images (Inline Image)
    if (assetPath && cleanText(assetPath)) {
      // Handle multiple comma-separated image paths
      const imagePath = assetPath.split(',')[0];

      const imageAsset = await findAndUploadImage(imagePath, rmc);
      if (imageAsset) {
        content.push({
          _key: generateKey(),
          _type: 'inlineImage',
          asset: imageAsset,
          alt: ' ',
          widthRatio: '1/2',
          centerImage: true,
        });
      }
    }
  }

  return content;
}

/**
 * Read Excel data
 */
function readExcelData() {
  console.log('📖 Reading Excel file...');

  if (!fs.existsSync(SPECS_FILE)) {
    console.error(`❌ Excel file not found: ${SPECS_FILE}`);
    process.exit(1);
  }

  try {
    const workbook = XLSX.readFile(SPECS_FILE);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    console.log(`✅ Read ${data.length} rows from Excel`);
    return data;
  } catch (error) {
    console.error('❌ Error reading Excel file:', error.message);
    process.exit(1);
  }
}

/**
 * Find watch by RMC
 */
async function findWatchByRMC(rmc) {
  try {
    const query = `*[_type == "rolexWatch" && rmc == $rmc && defined(parentWatch)][0] {
      _id,
      title,
      rmc,
      content[] {
        _type,
        _key,
        ...,
        content[] {
          _type,
          _key,
          ...
        }
      }
    }`;

    const watch = await client.fetch(query, { rmc });
    return watch;
  } catch (error) {
    console.error(`❌ Error finding watch with RMC ${rmc}:`, error.message);
    return null;
  }
}

/**
 * Update watch rich text content
 */
async function updateWatchRichText(watch, richTextContent, dryRun = true) {
  try {
    // Find existing Rolex Rich Text Calibre component
    let richTextComponent = watch.content?.find((item) => item._type === 'rolexRichTextCalibre');

    if (!richTextComponent) {
      console.log(`   ⚠️  No Rolex Rich Text Calibre component found, creating new one`);
      richTextComponent = {
        _key: generateKey(),
        _type: 'rolexRichTextCalibre',
        content: richTextContent,
      };

      // Add to content array
      const updatedContent = [...(watch.content || []), richTextComponent];

      if (dryRun) {
        console.log(`   🔍 DRY RUN: Would create new Rolex Rich Text Calibre component`);
        return true;
      }

      await client.patch(watch._id).set({ content: updatedContent }).commit();
    } else {
      console.log(`   📝 Updating existing Rolex Rich Text Calibre component`);

      if (dryRun) {
        console.log(`   🔍 DRY RUN: Would update rich text content`);
        return true;
      }

      // Update the specific component in the content array
      const updatedContent = watch.content.map((item) =>
        item._key === richTextComponent._key ? { ...item, content: richTextContent } : item
      );

      await client.patch(watch._id).set({ content: updatedContent }).commit();
    }

    console.log(`   ✅ Successfully updated rich text content`);
    return true;
  } catch (error) {
    console.error(`   ❌ Error updating watch:`, error.message);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  const rmcFilter = args.find((arg) => arg.startsWith('--rmc='))?.split('=')[1];

  console.log('📝 Rolex Rich Text Content Update Script');
  console.log('=========================================');

  if (dryRun) {
    console.log('🔍 Running in DRY RUN mode - no changes will be made');
  } else {
    console.log('⚠️  EXECUTE mode - content will be updated!');
  }

  if (rmcFilter) {
    console.log(`🎯 Filtering by RMC: ${rmcFilter}`);
  }

  // Read Excel data
  const excelData = readExcelData();

  // Filter data if RMC specified
  const filteredData = rmcFilter ? excelData.filter((row) => row.RMC === rmcFilter) : excelData;

  if (filteredData.length === 0) {
    console.log('❌ No matching data found');
    return;
  }

  console.log(`\n🔄 Processing ${filteredData.length} watches...`);

  let processedCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  for (const excelRow of filteredData) {
    const rmc = excelRow.RMC;

    if (!rmc) {
      console.log(`⚠️  Skipping row without RMC`);
      continue;
    }

    console.log(`\n📍 Processing ${rmc}...`);
    processedCount++;

    // Find watch in Sanity
    const watch = await findWatchByRMC(rmc);

    if (!watch) {
      console.log(`   ❌ Watch not found in Sanity`);
      errorCount++;
      continue;
    }

    console.log(`   ✅ Found watch: ${watch.title}`);

    // Debug: Show Excel data for this RMC
    console.log(`   📋 Excel data for ${rmc}:`);
    for (let i = 1; i <= 3; i++) {
      const title = excelRow[`Feature${i}_Title`];
      const text = excelRow[`Feature${i}_Text`];
      const asset = excelRow[`Feature${i}_Asset`];

      console.log(`     Feature${i}_Title: ${title || 'EMPTY'}`);
      console.log(`     Feature${i}_Text: ${text || 'EMPTY'}`);
      console.log(`     Feature${i}_Asset: ${asset || 'EMPTY'}`);
    }

    // Create rich text content
    const richTextContent = await createRichTextContent(excelRow, rmc);

    if (richTextContent.length === 0) {
      console.log(`   ⚠️  No rich text content to add`);
      continue;
    }

    console.log(`   📝 Generated ${richTextContent.length} content blocks`);

    // Update watch
    const success = await updateWatchRichText(watch, richTextContent, dryRun);

    if (success) {
      updatedCount++;
    } else {
      errorCount++;
    }
  }

  // Summary
  console.log('\n📊 Summary:');
  console.log(`   Processed: ${processedCount} watches`);
  console.log(`   Updated: ${updatedCount} watches`);
  console.log(`   Errors: ${errorCount} watches`);

  if (dryRun) {
    console.log('\n💡 Usage options:');
    console.log('  --execute              Actually update the content');
    console.log('  --rmc=M123456          Process only specific RMC');
    console.log('\nExample: node scripts/update-rolex-rich-text.js --rmc=M126505-0001 --execute');
  }
}

// Run the script
main().catch(console.error);

export { createRichTextContent, updateWatchRichText };
