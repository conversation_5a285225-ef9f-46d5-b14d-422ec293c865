import aspectRatio from '@tailwindcss/aspect-ratio';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';
import type { Config } from 'tailwindcss';
import animate from 'tailwindcss-animate';
import type { PluginUtils } from 'tailwindcss/types/config';

export default {
  darkMode: ['class'],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  safelist: ['text-rolex-green-400'],
  theme: {
    extend: {
      screens: {
        sm: '767px',
        md: '1024px',
        lg: '1440px',
        xl: '1920px',
        '3xl': '2560px',
      },
      colors: {
        background: '#f5f5f5',
        foreground: {
          500: 'rgb(74, 74, 74)',
          600: 'rgb(17, 24, 39);',
        },
        accent: 'rgb(178, 160, 119)',
        rolex: {
          green: {
            400: '#61BD93',
            500: '#127749',
            600: '#006039',
          },
          brown: '#452C1E',
          black: '#212121',
          grey: {
            400: '#f9f7f4',
            500: '#D4D4D4',
            600: '#767676',
          },
          beige: {
            400: '#F9F7F4',
            500: '#F4EFEA',
          },
        },

        // 2025-06-17
        primary: '#127749',
      },
      backgroundImage: {
        rolexNavigation: 'background-image: linear-gradient(90deg, #0b3e27, #197149)',
        rolexButton: 'linear-gradient(90deg, #006039, #1b915b)',
      },
      fontFamily: {
        heading: ['trajan-pro-3', 'sans'],
        body: ['rig-sans', 'sans'],
        rolex: ['Helvetica', 'sans'],
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        'slide-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-collapsible-content-height)' },
        },
        'slide-up': {
          from: { height: 'var(--radix-collapsible-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'slide-down': 'slide-down 0.3s ease-out',
        'slide-up': 'slide-up 0.3s ease-out',
      },
      typography: ({ theme }: PluginUtils) => ({
        rolex: {
          css: {
            '--tw-prose-body': theme('colors.rolex.brown'),
            '--tw-prose-headings': theme('colors.rolex.brown'),
            '--tw-prose-lead': theme('colors.rolex.brown'),
            '--tw-prose-links': theme('colors.rolex.brown'),
            '--tw-prose-bold': theme('colors.rolex.brown'),
            '--tw-prose-counters': theme('colors.rolex.brown'),
            '--tw-prose-bullets': theme('colors.rolex.brown'),
            '--tw-prose-hr': theme('colors.rolex.brown'),
            '--tw-prose-quotes': theme('colors.rolex.brown'),
            '--tw-prose-quote-borders': theme('colors.rolex.brown'),
            '--tw-prose-captions': theme('colors.rolex.brown'),
            '--tw-prose-code': theme('colors.rolex.brown'),
            '--tw-prose-pre-code': theme('colors.rolex.brown'),
            '--tw-prose-pre-bg': theme('colors.rolex.brown'),
            '--tw-prose-th-borders': theme('colors.rolex.brown'),
            '--tw-prose-td-borders': theme('colors.rolex.brown'),
          },
        },
      }),
    },
  },
  plugins: [typography, forms, aspectRatio, animate],
} satisfies Config;
